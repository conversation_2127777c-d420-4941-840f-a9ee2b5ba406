@echo off
echo ========================================
echo Building Complete RS485 Driver Solution
echo ========================================

REM Set Visual Studio environment
call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" 2>nul
if errorlevel 1 (
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
    if errorlevel 1 (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat" 2>nul
        if errorlevel 1 (
            call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
            if errorlevel 1 (
                echo ERROR: Could not find Visual Studio environment
                echo Please install Visual Studio 2019 or 2022 with C++ support
                pause
                exit /b 1
            )
        )
    )
)

echo Visual Studio environment loaded successfully

REM Create output directory
if not exist "FinalOutput" mkdir "FinalOutput"

echo.
echo ========================================
echo Step 1: Building RS485 Test UI (Fixed)
echo ========================================

REM Clean previous UI builds
if exist "RS485TestUI_Enhanced_Final_Fixed.exe" del "RS485TestUI_Enhanced_Final_Fixed.exe"
if exist "RS485TestUI_Enhanced_Final_Fixed.obj" del "RS485TestUI_Enhanced_Final_Fixed.obj"

echo Compiling RS485TestUI_Enhanced_Final_Fixed.cpp...

cl.exe /EHsc /MT /O2 ^
    /D "WIN32" /D "_WINDOWS" /D "UNICODE" /D "_UNICODE" ^
    /I "." ^
    RS485TestUI_Enhanced_Final_Fixed.cpp ^
    /link ^
    user32.lib gdi32.lib comctl32.lib setupapi.lib advapi32.lib ^
    /SUBSYSTEM:WINDOWS ^
    /OUT:RS485TestUI_Enhanced_Final_Fixed.exe

if errorlevel 1 (
    echo ❌ UI compilation failed!
    pause
    exit /b 1
)

copy "RS485TestUI_Enhanced_Final_Fixed.exe" "FinalOutput\" >nul
if exist "RS485TestUI_Enhanced_Final_Fixed.obj" del "RS485TestUI_Enhanced_Final_Fixed.obj"

echo ✅ Test UI compiled successfully

echo.
echo ========================================
echo Step 2: Building Driver Simulator
echo ========================================

REM Clean previous driver builds
if exist "RS485DriverSimple.exe" del "RS485DriverSimple.exe"
if exist "RS485DriverSimple.obj" del "RS485DriverSimple.obj"

echo Compiling RS485DriverSimple.cpp...

cl.exe /EHsc /MT /O2 ^
    /D "WIN32" /D "_CONSOLE" /D "UNICODE" /D "_UNICODE" ^
    /I "." ^
    RS485DriverSimple.cpp ^
    /link ^
    user32.lib ^
    /SUBSYSTEM:CONSOLE ^
    /OUT:RS485DriverSimple.exe

if errorlevel 1 (
    echo ❌ Driver simulator compilation failed!
    pause
    exit /b 1
)

copy "RS485DriverSimple.exe" "FinalOutput\" >nul
if exist "RS485DriverSimple.obj" del "RS485DriverSimple.obj"

echo ✅ Driver simulator compiled successfully

echo.
echo ========================================
echo Build Summary
echo ========================================

echo Checking output files...

if exist "FinalOutput\RS485TestUI_Enhanced_Final_Fixed.exe" (
    echo ✅ RS485TestUI_Enhanced_Final_Fixed.exe - Ready
) else (
    echo ❌ RS485TestUI_Enhanced_Final_Fixed.exe - Missing
)

if exist "FinalOutput\RS485DriverSimple.exe" (
    echo ✅ RS485DriverSimple.exe - Ready
) else (
    echo ❌ RS485DriverSimple.exe - Missing
)

echo.
echo ========================================
echo All builds completed!
echo Location: %CD%\FinalOutput\
echo ========================================

echo.
echo Files ready for testing:
echo 1. RS485TestUI_Enhanced_Final_Fixed.exe - Main test interface
echo 2. RS485DriverSimple.exe - Driver simulator for testing

echo.
echo Would you like to start the test UI now? (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    echo Starting RS485 Test UI...
    start "" "FinalOutput\RS485TestUI_Enhanced_Final_Fixed.exe"
    echo.
    echo Would you like to start the driver simulator too? (Y/N)
    set /p choice2=
    if /i "%choice2%"=="Y" (
        echo Starting Driver Simulator...
        start "" "FinalOutput\RS485DriverSimple.exe"
    )
)

echo.
pause
