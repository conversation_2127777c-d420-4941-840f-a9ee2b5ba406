@echo off
echo ===================================================================
echo Building RS485 Test UI Application - Complete Fixed Version
echo ===================================================================
echo.

REM Set up Visual Studio environment - try multiple paths
set "VS_FOUND=0"

if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat" (
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat" -arch=x64 >nul 2>&1
    if not errorlevel 1 set "VS_FOUND=1"
)

if "%VS_FOUND%"=="0" if exist "C:\Program Files (x86)\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat" (
    call "C:\Program Files (x86)\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat" -arch=x64 >nul 2>&1
    if not errorlevel 1 set "VS_FOUND=1"
)

if "%VS_FOUND%"=="0" if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\Tools\VsDevCmd.bat" (
    call "C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\Tools\VsDevCmd.bat" -arch=x64 >nul 2>&1
    if not errorlevel 1 set "VS_FOUND=1"
)

if "%VS_FOUND%"=="0" (
    echo ❌ Failed to find Visual Studio environment
    echo Please ensure Visual Studio 2022 is installed
    echo.
    pause
    exit /b 1
)

echo ✓ Visual Studio environment configured successfully
echo.

REM Clean previous builds
if exist "RS485TestUI_Complete_Fixed.exe" del "RS485TestUI_Complete_Fixed.exe"
if exist "RS485TestUI_Complete_Fixed.obj" del "RS485TestUI_Complete_Fixed.obj"
if exist "*.pdb" del "*.pdb"

echo Compiling RS485TestUI_Complete_Fixed.cpp...
echo.
echo Features included:
echo - Complete English interface with no Chinese text
echo - Robust window initialization and error handling
echo - Auto COM port detection and selection
echo - All 8 RS485 commands (S001, S002, U001-U006)
echo - Parameter validation and range checking
echo - Real-time hex data display and logging
echo - Connection status monitoring
echo - Test all commands functionality
echo - Raw data transmission capability
echo.

REM Compile with proper flags for Windows application
cl.exe /EHsc /MT /O2 ^
    /D "WIN32" /D "_WINDOWS" /D "UNICODE" /D "_UNICODE" ^
    /I "." ^
    /W3 ^
    RS485TestUI_Complete_Fixed.cpp ^
    /link ^
    user32.lib gdi32.lib comctl32.lib setupapi.lib advapi32.lib ^
    /SUBSYSTEM:WINDOWS ^
    /OUT:RS485TestUI_Complete_Fixed.exe

if errorlevel 1 (
    echo.
    echo ❌ Compilation failed!
    echo.
    echo Common issues and solutions:
    echo 1. Missing Visual Studio 2022 - Install from Microsoft website
    echo 2. Missing Windows SDK - Install via Visual Studio Installer
    echo 3. Source file errors - Check RS485TestUI_Complete_Fixed.cpp exists
    echo 4. Library linking issues - Ensure all required libraries are available
    echo.
    echo Detailed error information should be displayed above.
    echo.
    pause
    exit /b 1
)

echo.
echo ===================================================================
echo ✅ RS485 Test UI Application compiled successfully!
echo ===================================================================
echo.
echo Generated: RS485TestUI_Complete_Fixed.exe
echo File size: 
for %%f in (RS485TestUI_Complete_Fixed.exe) do echo %%~zf bytes
echo.
echo Key Features Implemented:
echo ✓ Complete English interface (no Chinese text)
echo ✓ Robust window creation with proper error handling
echo ✓ Auto-detection of all available COM ports
echo ✓ S001: RS485 slave address configuration (1-31)
echo ✓ S002: Baud rate selection (9600-115200)
echo ✓ U001: SEL detection threshold (40-500 mA)
echo ✓ U002: SEL max amplitude threshold (1000-2000 mA)
echo ✓ U003: Detection count before power cycle (1-5)
echo ✓ U004: Power cycle duration (200-1000 ms)
echo ✓ U005: GPIO input channel and enable/disable
echo ✓ U006: GPIO output channel and enable/disable
echo ✓ Test All Commands - Sequential testing of all functions
echo ✓ Real-time hex data display and transmission
echo ✓ Comprehensive logging with timestamps
echo ✓ Connection status monitoring and error reporting
echo ✓ Raw data transmission capability
echo ✓ Parameter validation and range checking
echo.
echo Copy to FinalOutput directory...
if not exist "FinalOutput" mkdir "FinalOutput"
copy RS485TestUI_Complete_Fixed.exe FinalOutput\ >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Warning: Could not copy to FinalOutput directory
) else (
    echo ✓ Successfully copied to FinalOutput directory
)
echo.
echo ===================================================================
echo Ready to Use!
echo ===================================================================
echo.
echo To run the application:
echo 1. Double-click: RS485TestUI_Complete_Fixed.exe
echo 2. Or from FinalOutput: .\FinalOutput\RS485TestUI_Complete_Fixed.exe
echo.
echo Usage Instructions:
echo 1. Click "Refresh Ports" to scan for available COM ports
echo 2. Select your RS485 device port from the dropdown
echo 3. Click "Connect" to establish connection
echo 4. Use individual command buttons to test specific functions
echo 5. Use "Test All Commands" to run complete test sequence
echo 6. Monitor results in the log window
echo 7. Click "Disconnect" when finished
echo.
echo Troubleshooting:
echo - If no ports appear: Check RS485 device connection and drivers
echo - If connection fails: Verify port is not in use by other applications
echo - If commands fail: Check device power and RS485 wiring
echo.

pause
