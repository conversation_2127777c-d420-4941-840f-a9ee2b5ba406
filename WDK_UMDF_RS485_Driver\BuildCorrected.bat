@echo off
echo ========================================
echo Building RS485TestUI_CORRECTED.exe
echo ========================================
echo.
echo This version uses the EXACT same pattern as the working RS485TestUI_Complete.exe
echo.

REM Try different Visual Studio paths
call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
if errorlevel 1 (
    call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" 2>nul
    if errorlevel 1 (
        echo ERROR: Could not find Visual Studio environment
        pause
        exit /b 1
    )
)

echo Visual Studio environment loaded successfully
echo.

REM Clean previous builds
if exist "RS485TestUI_CORRECTED.exe" del "RS485TestUI_CORRECTED.exe"
if exist "RS485TestUI_CORRECTED.obj" del "RS485TestUI_CORRECTED.obj"

echo Compiling RS485TestUI_CORRECTED.cpp...
echo.

REM Compile with exact same flags as working version
cl.exe ^
    /EHsc ^
    /std:c++17 ^
    /Fe:RS485TestUI_CORRECTED.exe ^
    /DWIN32 ^
    /D_WINDOWS ^
    /DUNICODE ^
    /D_UNICODE ^
    /O2 ^
    RS485TestUI_CORRECTED.cpp ^
    /link ^
    user32.lib ^
    gdi32.lib ^
    comctl32.lib ^
    /SUBSYSTEM:WINDOWS

if %errorLevel% equ 0 (
    echo.
    echo ========================================
    echo ✅ RS485TestUI_CORRECTED.exe compiled successfully!
    echo ========================================
    echo.
    echo Generated: RS485TestUI_CORRECTED.exe
    echo.
    echo This version uses the EXACT working pattern:
    echo ✓ cbWndExtra = sizeof(RS485TestApp*)
    echo ✓ SetWindowLongPtr(hWnd, 0, (LONG_PTR)pApp)
    echo ✓ Direct control creation in WM_CREATE
    echo ✓ Proper InitCommonControls() initialization
    echo ✓ All RS485 test buttons and functionality
    echo.
    
    REM Copy to FinalOutput
    if not exist "FinalOutput" mkdir "FinalOutput"
    copy "RS485TestUI_CORRECTED.exe" "FinalOutput\" >nul
    echo ✅ Copied to FinalOutput directory
    echo.
    
    echo You can now run: .\RS485TestUI_CORRECTED.exe
    echo Or from FinalOutput: .\FinalOutput\RS485TestUI_CORRECTED.exe
    echo.
) else (
    echo.
    echo ❌ Compilation failed!
    echo Please check the error messages above.
)

echo.
pause
