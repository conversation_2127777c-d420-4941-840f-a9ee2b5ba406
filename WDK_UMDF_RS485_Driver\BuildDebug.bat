@echo off
echo Building Debug Console Test...

call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
if errorlevel 1 (
    call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" 2>nul
)

cl.exe /EHsc /DUNICODE /D_UNICODE RS485TestUI_Console_Debug.cpp /link user32.lib gdi32.lib /SUBSYSTEM:WINDOWS /OUT:RS485TestUI_Console_Debug.exe

if errorlevel 1 (
    echo Compilation failed
    pause
    exit /b 1
)

echo Compilation successful
echo Running debug test...
RS485TestUI_Console_Debug.exe

pause
