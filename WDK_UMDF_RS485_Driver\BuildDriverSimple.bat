@echo off
echo ========================================
echo Building RS485 Driver Simulator
echo ========================================

REM Set Visual Studio environment
call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" 2>nul
if errorlevel 1 (
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
    if errorlevel 1 (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat" 2>nul
        if errorlevel 1 (
            call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
            if errorlevel 1 (
                echo ERROR: Could not find Visual Studio environment
                echo Please install Visual Studio 2019 or 2022 with C++ support
                pause
                exit /b 1
            )
        )
    )
)

echo Visual Studio environment loaded successfully

REM Clean previous builds
if exist "RS485DriverSimple.exe" del "RS485DriverSimple.exe"
if exist "RS485DriverSimple.obj" del "RS485DriverSimple.obj"

echo.
echo Compiling RS485DriverSimple.cpp...

REM Compile with proper flags
cl.exe /EHsc /MT /O2 ^
    /D "WIN32" /D "_CONSOLE" /D "UNICODE" /D "_UNICODE" ^
    /I "." ^
    RS485DriverSimple.cpp ^
    /link ^
    user32.lib ^
    /SUBSYSTEM:CONSOLE ^
    /OUT:RS485DriverSimple.exe

if errorlevel 1 (
    echo.
    echo ❌ Compilation failed!
    echo Check the error messages above
    pause
    exit /b 1
)

echo.
echo ✅ Compilation successful!

REM Copy to FinalOutput directory
if not exist "FinalOutput" mkdir "FinalOutput"
copy "RS485DriverSimple.exe" "FinalOutput\" >nul

echo ✅ Executable copied to FinalOutput directory

REM Clean intermediate files
if exist "RS485DriverSimple.obj" del "RS485DriverSimple.obj"

echo.
echo ========================================
echo Build completed successfully!
echo Output: RS485DriverSimple.exe
echo Location: %CD%\FinalOutput\
echo ========================================

echo.
pause
