@echo off
echo ========================================
echo Building RS485 Final Working UI
echo ========================================

REM Try different Visual Studio paths
set "VS_FOUND=0"

if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    if not errorlevel 1 set "VS_FOUND=1"
)

if "%VS_FOUND%"=="0" if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    if not errorlevel 1 set "VS_FOUND=1"
)

if "%VS_FOUND%"=="0" (
    echo ERROR: Could not find Visual Studio environment
    echo Please ensure Visual Studio 2022 is installed
    pause
    exit /b 1
)

echo Visual Studio environment loaded successfully

REM Clean previous builds
if exist "RS485TestUI_Final_Working.exe" del "RS485TestUI_Final_Working.exe"
if exist "RS485TestUI_Final_Working.obj" del "RS485TestUI_Final_Working.obj"

echo.
echo Compiling RS485TestUI_Final_Working.cpp...
echo This version is based on the working RS485TestUI_Complete.exe pattern

REM Compile with exact same flags as working version
cl.exe /EHsc /MT /O2 ^
    /D "WIN32" /D "_WINDOWS" /D "UNICODE" /D "_UNICODE" ^
    /I "." ^
    RS485TestUI_Final_Working.cpp ^
    /link ^
    user32.lib gdi32.lib comctl32.lib ^
    /SUBSYSTEM:WINDOWS ^
    /OUT:RS485TestUI_Final_Working.exe

if errorlevel 1 (
    echo.
    echo ❌ Compilation failed!
    echo Check the error messages above
    pause
    exit /b 1
)

echo.
echo ✅ Final Working UI compilation successful!

REM Copy to FinalOutput directory
if not exist "FinalOutput" mkdir "FinalOutput"
copy "RS485TestUI_Final_Working.exe" "FinalOutput\" >nul

echo ✅ Executable copied to FinalOutput directory

REM Clean intermediate files
if exist "RS485TestUI_Final_Working.obj" del "RS485TestUI_Final_Working.obj"

echo.
echo ========================================
echo Build completed successfully!
echo Output: RS485TestUI_Final_Working.exe
echo Location: %CD%\FinalOutput\
echo ========================================

echo.
echo This version is based on the working RS485TestUI_Complete.exe
echo It uses the same simple, proven approach that displays controls properly
echo.
echo Key features:
echo ✓ Simple window procedure without complex class setup
echo ✓ Direct control creation in WM_CREATE
echo ✓ Proper InitCommonControls() initialization
echo ✓ All RS485 test buttons and functionality
echo ✓ Real-time logging with timestamps
echo ✓ Exact same layout as working version
echo.

REM Test the executable
if exist "RS485TestUI_Final_Working.exe" (
    echo ✅ Executable exists and is ready to run
    echo.
    echo Would you like to run the final working version now? (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        echo Starting Final Working UI...
        start "" "RS485TestUI_Final_Working.exe"
    )
) else (
    echo ❌ Executable not found
)

echo.
pause
