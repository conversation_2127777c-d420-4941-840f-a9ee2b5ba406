@echo off
echo ========================================
echo Building RS485 Test UI - Fixed Version
echo ========================================

REM Set Visual Studio environment
call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" 2>nul
if errorlevel 1 (
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
    if errorlevel 1 (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat" 2>nul
        if errorlevel 1 (
            call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
            if errorlevel 1 (
                echo ERROR: Could not find Visual Studio environment
                echo Please install Visual Studio 2019 or 2022 with C++ support
                pause
                exit /b 1
            )
        )
    )
)

echo Visual Studio environment loaded successfully

REM Clean previous builds
if exist "RS485TestUI_Final_Working.exe" del "RS485TestUI_Final_Working.exe"
if exist "RS485TestUI_Final_Working.obj" del "RS485TestUI_Final_Working.obj"

echo.
echo Compiling RS485TestUI_Final_Working.cpp...
echo This version is based on the working RS485TestUI_Complete.exe pattern

REM Compile with proper flags
cl.exe /EHsc /MT /O2 ^
    /D "WIN32" /D "_WINDOWS" /D "UNICODE" /D "_UNICODE" ^
    /I "." ^
    RS485TestUI_Final_Working.cpp ^
    /link ^
    user32.lib gdi32.lib comctl32.lib ^
    /SUBSYSTEM:WINDOWS ^
    /OUT:RS485TestUI_Final_Working.exe

if errorlevel 1 (
    echo.
    echo ❌ Compilation failed!
    echo Check the error messages above
    pause
    exit /b 1
)

echo.
echo ✅ Compilation successful!

REM Copy to FinalOutput directory
if not exist "FinalOutput" mkdir "FinalOutput"
copy "RS485TestUI_Final_Working.exe" "FinalOutput\" >nul

echo ✅ Executable copied to FinalOutput directory

REM Clean intermediate files
if exist "RS485TestUI_Final_Working.obj" del "RS485TestUI_Final_Working.obj"

echo.
echo ========================================
echo Build completed successfully!
echo Output: RS485TestUI_Final_Working.exe
echo Location: %CD%\FinalOutput\
echo ========================================

REM Test the executable
echo.
echo Testing executable...
if exist "FinalOutput\RS485TestUI_Enhanced_Final_Fixed.exe" (
    echo ✅ Executable exists and is ready to run
    echo.
    echo Would you like to run the test UI now? (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        echo Starting RS485 Test UI...
        start "" "FinalOutput\RS485TestUI_Enhanced_Final_Fixed.exe"
    )
) else (
    echo ❌ Executable not found in FinalOutput directory
)

echo.
pause
