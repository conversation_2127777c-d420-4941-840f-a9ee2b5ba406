@echo off
echo ========================================
echo Fresh Build of RS485TestUI_CORRECTED.exe
echo ========================================

REM Kill any running instances
taskkill /f /im RS485TestUI_CORRECTED.exe >nul 2>&1

REM Wait a moment
timeout /t 2 /nobreak >nul

REM Clean up old files
del /f /q RS485TestUI_CORRECTED.exe >nul 2>&1
del /f /q RS485TestUI_CORRECTED.obj >nul 2>&1
del /f /q *.pdb >nul 2>&1

echo Cleaned old files

REM Set up Visual Studio environment
call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
if errorlevel 1 (
    call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    if errorlevel 1 (
        echo ERROR: Could not find Visual Studio
        pause
        exit /b 1
    )
)

echo Visual Studio environment loaded

REM Compile
echo Compiling RS485TestUI_CORRECTED.cpp...
cl.exe /EHsc /std:c++17 /DWIN32 /D_WINDOWS /DUNICODE /D_UNICODE /O2 RS485TestUI_CORRECTED.cpp /link user32.lib gdi32.lib comctl32.lib /SUBSYSTEM:WINDOWS /OUT:RS485TestUI_CORRECTED.exe

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo SUCCESS: RS485TestUI_CORRECTED.exe built successfully!
    echo ========================================
    
    REM Copy to FinalOutput
    if not exist "FinalOutput" mkdir "FinalOutput"
    copy /y "RS485TestUI_CORRECTED.exe" "FinalOutput\" >nul
    
    echo File copied to FinalOutput directory
    echo.
    echo Ready to test: .\RS485TestUI_CORRECTED.exe
    
    REM Clean intermediate files
    del /f /q RS485TestUI_CORRECTED.obj >nul 2>&1
    
) else (
    echo.
    echo ========================================
    echo FAILED: Compilation failed!
    echo ========================================
    echo Please check the error messages above
)

echo.
pause
