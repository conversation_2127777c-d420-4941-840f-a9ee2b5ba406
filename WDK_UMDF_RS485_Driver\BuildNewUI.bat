@echo off
echo ========================================
echo Building RS485 Test UI - Complete Fixed Version
echo ========================================

REM Set Visual Studio environment
call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" 2>nul
if errorlevel 1 (
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
    if errorlevel 1 (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat" 2>nul
        if errorlevel 1 (
            call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
            if errorlevel 1 (
                echo ERROR: Could not find Visual Studio environment
                echo Please install Visual Studio 2019 or 2022 with C++ support
                pause
                exit /b 1
            )
        )
    )
)

echo Visual Studio environment loaded successfully

REM Clean previous builds
if exist "RS485TestUI_Complete_Fixed.exe" del "RS485TestUI_Complete_Fixed.exe"
if exist "RS485TestUI_Complete_Fixed.obj" del "RS485TestUI_Complete_Fixed.obj"

echo.
echo Compiling RS485TestUI_Working_Fixed.cpp...
echo.
echo Features:
echo - Complete English interface (no Chinese text)
echo - Robust window initialization and error handling
echo - Auto COM port detection and selection
echo - All 8 RS485 commands (S001, S002, U001-U006)
echo - Parameter validation and range checking
echo - Real-time hex data display and logging
echo - Connection status monitoring
echo - Test all commands functionality
echo.

REM Compile with proper flags
cl.exe /EHsc /MT /O2 ^
    /D "WIN32" /D "_WINDOWS" /D "UNICODE" /D "_UNICODE" ^
    /I "." ^
    RS485TestUI_Working_Fixed.cpp ^
    /link ^
    user32.lib gdi32.lib comctl32.lib setupapi.lib advapi32.lib ^
    /SUBSYSTEM:WINDOWS ^
    /OUT:RS485TestUI_Working_Fixed.exe

if errorlevel 1 (
    echo.
    echo ❌ Compilation failed!
    echo Check the error messages above
    pause
    exit /b 1
)

echo.
echo ✅ Compilation successful!

REM Copy to FinalOutput directory
if not exist "FinalOutput" mkdir "FinalOutput"
copy "RS485TestUI_Working_Fixed.exe" "FinalOutput\" >nul

echo ✅ Executable copied to FinalOutput directory

REM Clean intermediate files
if exist "RS485TestUI_Working_Fixed.obj" del "RS485TestUI_Working_Fixed.obj"

echo.
echo ========================================
echo Build completed successfully!
echo Output: RS485TestUI_Working_Fixed.exe
echo Location: %CD%\FinalOutput\
echo ========================================

REM Test the executable
echo.
echo Testing executable...
if exist "FinalOutput\RS485TestUI_Working_Fixed.exe" (
    echo ✅ Executable exists and is ready to run
    echo.
    echo Key Features Implemented:
    echo ✓ Complete English interface (no Chinese text)
    echo ✓ Robust window creation with proper error handling
    echo ✓ Auto-detection of all available COM ports
    echo ✓ S001: RS485 slave address configuration (1-31)
    echo ✓ S002: Baud rate selection (9600-115200)
    echo ✓ U001: SEL detection threshold (40-500 mA)
    echo ✓ U002: SEL max amplitude threshold (1000-2000 mA)
    echo ✓ U003: Detection count before power cycle (1-5)
    echo ✓ U004: Power cycle duration (200-1000 ms)
    echo ✓ U005: GPIO input channel and enable/disable
    echo ✓ U006: GPIO output channel and enable/disable
    echo ✓ Test All Commands - Sequential testing of all functions
    echo ✓ Real-time hex data display and transmission
    echo ✓ Comprehensive logging with timestamps
    echo ✓ Connection status monitoring and error reporting
    echo.
    echo Would you like to run the test UI now? (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        echo Starting RS485 Test UI...
        start "" "FinalOutput\RS485TestUI_Working_Fixed.exe"
    )
) else (
    echo ❌ Executable not found in FinalOutput directory
)

echo.
pause
