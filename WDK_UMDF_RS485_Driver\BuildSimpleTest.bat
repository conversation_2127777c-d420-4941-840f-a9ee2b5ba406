@echo off
echo ========================================
echo Building RS485 Simple Test Application
echo ========================================

REM Set Visual Studio environment
call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" 2>nul
if errorlevel 1 (
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
    if errorlevel 1 (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat" 2>nul
        if errorlevel 1 (
            call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
            if errorlevel 1 (
                echo ERROR: Could not find Visual Studio environment
                echo Please install Visual Studio 2019 or 2022 with C++ support
                pause
                exit /b 1
            )
        )
    )
)

echo Visual Studio environment loaded successfully

REM Clean previous builds
if exist "RS485TestUI_Simple_Test.exe" del "RS485TestUI_Simple_Test.exe"
if exist "RS485TestUI_Simple_Test.obj" del "RS485TestUI_Simple_Test.obj"

echo.
echo Compiling RS485TestUI_Simple_Test.cpp...

REM Compile with proper flags
cl.exe /EHsc /MT /O2 ^
    /D "WIN32" /D "_WINDOWS" /D "UNICODE" /D "_UNICODE" ^
    /I "." ^
    RS485TestUI_Simple_Test.cpp ^
    /link ^
    user32.lib gdi32.lib comctl32.lib ^
    /SUBSYSTEM:WINDOWS ^
    /OUT:RS485TestUI_Simple_Test.exe

if errorlevel 1 (
    echo.
    echo ❌ Compilation failed!
    echo Check the error messages above
    pause
    exit /b 1
)

echo.
echo ✅ Simple test compilation successful!

REM Test the executable
echo.
echo Testing executable...
if exist "RS485TestUI_Simple_Test.exe" (
    echo ✅ Executable exists and is ready to run
    echo.
    echo Would you like to run the simple test now? (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        echo Starting Simple Test...
        start "" "RS485TestUI_Simple_Test.exe"
    )
) else (
    echo ❌ Executable not found
)

echo.
pause
