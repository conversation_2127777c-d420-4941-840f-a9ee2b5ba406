@echo off
echo ===================================================================
echo Building RS485 Test UI Application (Fixed Version)
echo ===================================================================
echo.

REM Set up Visual Studio environment
call "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat" -arch=x64 >nul 2>&1

echo Compiling RS485TestUI.cpp with all command buttons...
echo.

REM Compile the UI application with enhanced features
cl.exe ^
    /EHsc ^
    /std:c++17 ^
    /Fe:RS485TestUI_CORRECTED.exe ^
    /DWIN32 ^
    /D_WINDOWS ^
    /DUNICODE ^
    /D_UNICODE ^
    /O2 ^
    RS485TestUI_CORRECTED.cpp ^
    /link ^
    user32.lib ^
    gdi32.lib ^
    comctl32.lib ^
    setupapi.lib ^
    /SUBSYSTEM:WINDOWS

if %errorLevel% equ 0 (
    echo.
    echo ===================================================================
    echo ✅ RS485 Test UI compiled successfully!
    echo ===================================================================
    echo.
    echo Generated: RS485TestUI_CORRECTED.exe
    echo.
    echo Features included:
    echo - S001, S002 system configuration commands
    echo - U001, U002, U003, U004, U005, U006 user configuration commands
    echo - RS485 connection test functionality
    echo - Enhanced UI with organized button layout
    echo - Real-time status feedback
    echo.
    echo You can now run: .\RS485TestUI_CORRECTED.exe
    echo.
) else (
    echo.
    echo ❌ Compilation failed!
    echo Please check the error messages above.
)

echo.
pause
