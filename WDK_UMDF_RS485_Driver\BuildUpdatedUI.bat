@echo off
echo ===================================================================
echo Building RS485 Updated Test UI - S002 Removed, Improved Layout
echo ===================================================================
echo.

REM Set up Visual Studio environment
call "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat" -arch=x64 >nul 2>&1

if %errorLevel% neq 0 (
    echo ❌ Failed to set up Visual Studio environment
    echo Please ensure Visual Studio 2022 is installed
    pause
    exit /b 1
)

echo ✓ Visual Studio environment configured
echo.

echo Compiling Updated RS485 Test UI...
echo Changes:
echo - ❌ Removed S002 baud rate functionality (fixed to 9600 bps)
echo - ✅ Improved UI layout with consistent sizing
echo - ✅ Better alignment and visual organization
echo - ✅ Centered Execute button
echo - ✅ Consistent "Set" button naming
echo.

REM Compile the updated UI
cl.exe ^
    /EHsc ^
    /std:c++17 ^
    /Fe:RS485TestUI_Updated.exe ^
    /DWIN32 ^
    /D_WINDOWS ^
    /DUNICODE ^
    /D_UNICODE ^
    /O2 ^
    /W3 ^
    RS485TestUI_Enhanced_Final_Fixed.cpp ^
    /link ^
    user32.lib ^
    gdi32.lib ^
    comctl32.lib ^
    setupapi.lib ^
    advapi32.lib ^
    /SUBSYSTEM:WINDOWS

if %errorLevel% equ 0 (
    echo.
    echo ===================================================================
    echo ✅ Updated RS485 Test UI compiled successfully!
    echo ===================================================================
    echo.
    echo Generated: RS485TestUI_Updated.exe
    echo.
    echo Key Features:
    echo ✓ Auto-detects FTDI/RS485 COM ports
    echo ✓ Fixed baud rate at 9600 bps (S002 removed)
    echo ✓ S001: Slave address input (1-31)
    echo ✓ U001: SEL threshold input (40-500 mA)
    echo ✓ U002: Max amplitude input (1000-2000 mA)
    echo ✓ U003: Detection count dropdown (1-5)
    echo ✓ U004: Power cycle duration dropdown (200-1000 ms)
    echo ✓ U005: GPIO input with channel/enable dropdowns
    echo ✓ U006: GPIO output with channel/enable dropdowns
    echo ✓ Improved UI layout with consistent sizing
    echo ✓ Centered Execute All Commands button
    echo ✓ Real-time hex data display
    echo ✓ Connection status monitoring
    echo.
    echo Copy to FinalOutput directory...
    copy RS485TestUI_Updated.exe FinalOutput\ >nul 2>&1
    echo ✓ Copied to FinalOutput directory
    echo.
    echo Ready to use! Run: .\RS485TestUI_Updated.exe
    echo.
) else (
    echo.
    echo ❌ Compilation failed!
    echo Please check the error messages above.
    echo.
    echo Common issues:
    echo - Missing Visual Studio 2022
    echo - Missing Windows SDK
    echo - Source file errors
    echo.
)

echo.
pause
