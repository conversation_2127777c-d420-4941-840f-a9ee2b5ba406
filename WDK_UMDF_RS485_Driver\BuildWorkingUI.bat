@echo off
echo ========================================
echo Building RS485 Working Test UI
echo ========================================

REM Set Visual Studio environment
call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" 2>nul
if errorlevel 1 (
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
    if errorlevel 1 (
        echo ERROR: Could not find Visual Studio environment
        pause
        exit /b 1
    )
)

echo Visual Studio environment loaded successfully

REM Clean previous builds
if exist "RS485TestUI_Working_Fixed.exe" del "RS485TestUI_Working_Fixed.exe"
if exist "RS485TestUI_Working_Fixed.obj" del "RS485TestUI_Working_Fixed.obj"

echo.
echo Compiling RS485TestUI_Working_Fixed.cpp...

REM Compile with proper flags
cl.exe /EHsc /MT /O2 ^
    /D "WIN32" /D "_WINDOWS" /D "UNICODE" /D "_UNICODE" ^
    /I "." ^
    RS485TestUI_Working_Fixed.cpp ^
    /link ^
    user32.lib gdi32.lib comctl32.lib ^
    /SUBSYSTEM:WINDOWS ^
    /OUT:RS485TestUI_Working_Fixed.exe

if errorlevel 1 (
    echo.
    echo ❌ Compilation failed!
    echo Check the error messages above
    pause
    exit /b 1
)

echo.
echo ✅ Working UI compilation successful!

REM Copy to FinalOutput directory
if not exist "FinalOutput" mkdir "FinalOutput"
copy "RS485TestUI_Working_Fixed.exe" "FinalOutput\" >nul

echo ✅ Executable copied to FinalOutput directory

REM Clean intermediate files
if exist "RS485TestUI_Working_Fixed.obj" del "RS485TestUI_Working_Fixed.obj"

echo.
echo ========================================
echo Build completed successfully!
echo Output: RS485TestUI_Working_Fixed.exe
echo Location: %CD%\FinalOutput\
echo ========================================

REM Test the executable
echo.
echo Testing executable...
if exist "RS485TestUI_Working_Fixed.exe" (
    echo ✅ Executable exists and is ready to run
    echo.
    echo This version should display all controls properly!
    echo.
    echo Would you like to run the working test now? (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        echo Starting Working Test UI...
        start "" "RS485TestUI_Working_Fixed.exe"
    )
) else (
    echo ❌ Executable not found
)

echo.
pause
