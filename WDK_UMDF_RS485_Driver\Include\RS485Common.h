#ifndef RS485_COMMON_H
#define RS485_COMMON_H

//
// RS485 Driver Common Definitions
// Windows User-Mode Driver Framework (UMDF) Implementation
// Based on ZES Protocol Specification
//

#include <windows.h>
#include <wdf.h>
#include <initguid.h>
#include <devguid.h>
#include <setupapi.h>
#include <strsafe.h>

//
// Protocol Constants
//
#define RS485_FRAME_SIZE            16      // Complete frame size (Header + ID + Payload + CRC + Trailer)
#define RS485_PAYLOAD_SIZE          12      // Core payload size (Key 4 bytes + Value 8 bytes)
#define RS485_HEADER_BYTE           0xAA    // Frame start marker
#define RS485_TRAILER_BYTE          0x0D    // Frame end marker

//
// Buffer Configuration
//
#define RS485_UPLINK_BUFFER_SLOTS   5       // PC to device buffer capacity (5 × 12 bytes = 60 bytes)
#define RS485_DOWNLINK_BUFFER_SLOTS 10      // Device to PC buffer capacity (10 × 12 bytes = 120 bytes)
#define RS485_TOTAL_BUFFER_SIZE     (RS485_UPLINK_BUFFER_SLOTS * RS485_PAYLOAD_SIZE + \
                                     RS485_DOWNLINK_BUFFER_SLOTS * RS485_PAYLOAD_SIZE)

//
// Custom Status Codes for UMDF
//
#ifndef STATUS_BUFFER_UNDERFLOW
#define STATUS_BUFFER_UNDERFLOW     ((NTSTATUS)0xC0000093L)
#endif
#ifndef STATUS_BUFFER_OVERFLOW
#define STATUS_BUFFER_OVERFLOW      ((NTSTATUS)0x80000005L)
#endif

//
// Function Codes (3 bits in ID byte)
//
#define RS485_FUNCTION_ASSIGN_DATA      0b111   // Master: Assign data (S/U/W-series)
#define RS485_FUNCTION_REQUEST_DATA     0b110   // Master: Request data (A-series)
#define RS485_FUNCTION_RESPONSE_ASSIGN  0b010   // Slave: Response to assign
#define RS485_FUNCTION_RESPONSE_REQUEST 0b001   // Slave: Response to request
#define RS485_FUNCTION_RESEND_REQUEST   0b000   // Both: Re-send request

//
// Device Address Constants
//
#define RS485_BROADCAST_ADDRESS     0x00    // Broadcast address for S-series commands
#define RS485_MIN_SLAVE_ADDRESS     0x01    // Minimum valid slave address
#define RS485_MAX_SLAVE_ADDRESS     0x1F    // Maximum valid slave address (31 devices)

//
// Timeout and Retry Configuration
//
#define RS485_RESPONSE_TIMEOUT_MS   100     // Response window time
#define RS485_MAX_RETRY_COUNT       3       // Maximum retry attempts
#define RS485_RETRY_DELAY_MS        50      // Delay between retries

//
// IOCTL Codes for Driver Communication
//
#define IOCTL_RS485_CONFIGURE_SYSTEM    CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_CONFIGURE_USER      CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x801, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_REQUEST_DATA        CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x802, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_RECEIVE_RESPONSE    CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x803, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_MODEL_DATA_OP       CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x804, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_GET_BUFFER_STATUS   CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x805, METHOD_BUFFERED, FILE_READ_ACCESS)
#define IOCTL_RS485_CLEAR_BUFFER        CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x806, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_SET_BUFFER_POLICY   CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x807, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_GET_HW_STATUS       CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x808, METHOD_BUFFERED, FILE_READ_ACCESS)
#define IOCTL_RS485_GET_PERFORMANCE     CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x809, METHOD_BUFFERED, FILE_READ_ACCESS)
#define IOCTL_RS485_CHECK_BUFFER_FLAGS  CTL_CODE(FILE_DEVICE_SERIAL_PORT, 0x80A, METHOD_BUFFERED, FILE_READ_ACCESS)

//
// Device Interface GUID
// {12345678-1234-1234-1234-123456789ABC}
//
DEFINE_GUID(GUID_DEVINTERFACE_RS485_FILTER,
    0x12345678, 0x1234, 0x1234, 0x12, 0x34, 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC);

//
// Driver Registry Keys
//
#define RS485_DRIVER_SERVICE_NAME   L"RS485FilterDriver"
#define RS485_DEVICE_NAME           L"\\Device\\RS485Filter"
#define RS485_SYMBOLIC_LINK_NAME    L"\\DosDevices\\RS485Filter"

//
// Debug and Logging Macros for UMDF
//
#if DBG
#define RS485_DEBUG_PRINT(format, ...) \
    OutputDebugStringA("[RS485Filter] " format "\n")
#else
#define RS485_DEBUG_PRINT(format, ...)
#endif

#define RS485_ERROR_PRINT(format, ...) \
    OutputDebugStringA("[RS485Filter ERROR] " format "\n")

#define RS485_WARNING_PRINT(format, ...) \
    OutputDebugStringA("[RS485Filter WARNING] " format "\n")

//
// Memory Pool Tags
//
#define RS485_POOL_TAG_GENERAL      'G58R'  // 'R85G'
#define RS485_POOL_TAG_BUFFER       'B58R'  // 'R85B'
#define RS485_POOL_TAG_FRAME        'F58R'  // 'R85F'
#define RS485_POOL_TAG_CONTEXT      'C58R'  // 'R85C'

//
// Driver Version Information
//
#define RS485_DRIVER_VERSION_MAJOR  1
#define RS485_DRIVER_VERSION_MINOR  0
#define RS485_DRIVER_VERSION_BUILD  0
#define RS485_DRIVER_VERSION_REVISION 0

//
// Performance and Statistics
//
#define RS485_STATS_HISTORY_SIZE    100     // Number of performance samples to keep
#define RS485_BUFFER_THRESHOLD_DEFAULT 80   // Default buffer threshold percentage

//
// CRC8 Polynomial for ZES Protocol
// Polynomial: 0x97 = x^8 + x^5 + x^3 + x^2 + x + 1
//
#define RS485_CRC8_POLYNOMIAL       0x97
#define RS485_CRC8_INITIAL_VALUE    0x00

//
// Forward Declarations
//
typedef struct _RS485_DEVICE_CONTEXT RS485_DEVICE_CONTEXT, *PRS485_DEVICE_CONTEXT;
typedef struct _RS485_QUEUE_CONTEXT RS485_QUEUE_CONTEXT, *PRS485_QUEUE_CONTEXT;
typedef struct _RS485_BUFFER_CONTEXT RS485_BUFFER_CONTEXT, *PRS485_BUFFER_CONTEXT;
typedef struct _RS485_FRAME RS485_FRAME, *PRS485_FRAME;

//
// Function Declarations
//
NTSTATUS RS485DriverEntry(_In_ PDRIVER_OBJECT DriverObject, _In_ PUNICODE_STRING RegistryPath);
VOID RS485DriverUnload(_In_ PDRIVER_OBJECT DriverObject);

//
// Utility Macros
//
#define RS485_EXTRACT_FUNCTION_CODE(idByte)     (((idByte) >> 5) & 0x07)
#define RS485_EXTRACT_DEVICE_ADDRESS(idByte)    ((idByte) & 0x1F)
#define RS485_BUILD_ID_BYTE(funcCode, address)  (((funcCode) << 5) | ((address) & 0x1F))

#define RS485_IS_VALID_SLAVE_ADDRESS(addr)      ((addr) >= RS485_MIN_SLAVE_ADDRESS && \
                                                 (addr) <= RS485_MAX_SLAVE_ADDRESS)

#define RS485_IS_BROADCAST_ADDRESS(addr)        ((addr) == RS485_BROADCAST_ADDRESS)

//
// Alignment and Padding Macros
//
#define RS485_ALIGN_UP(value, alignment)        (((value) + (alignment) - 1) & ~((alignment) - 1))
#define RS485_ALIGN_DOWN(value, alignment)      ((value) & ~((alignment) - 1))

//
// String Conversion Utilities
//
#define RS485_MAX_COMMAND_KEY_LENGTH    4       // Maximum length for command keys (e.g., "S001")
#define RS485_MAX_ERROR_MESSAGE_LENGTH  256     // Maximum error message length

//
// Buffer Management Constants
//
#define RS485_BUFFER_SIGNATURE          'FUBR'  // 'RBUF' - Buffer signature for validation
#define RS485_INVALID_SEQUENCE_NUMBER   0xFFFFFFFF

//
// Hardware Interface Constants
//
#define RS485_FTDI_VID                  0x0403  // FTDI Vendor ID
#define RS485_FTDI_PID                  0x6001  // FTDI Product ID (FT232R)
#define RS485_DEFAULT_BAUD_RATE         9600    // Default baud rate

#endif // RS485_COMMON_H
