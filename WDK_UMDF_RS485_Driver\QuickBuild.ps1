# Quick build script for RS485TestUI_CORRECTED.cpp
Write-Host "Building RS485TestUI_CORRECTED.exe..." -ForegroundColor Green

# Set Visual Studio environment
$vsPath = "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
if (-not (Test-Path $vsPath)) {
    $vsPath = "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat"
}

if (Test-Path $vsPath) {
    Write-Host "Found Visual Studio at: $vsPath" -ForegroundColor Yellow
    
    # Build command
    $buildCmd = "call `"$vsPath`" && cl.exe /EHsc /std:c++17 /Fe:RS485TestUI_CORRECTED.exe /DWIN32 /D_WINDOWS /DUNICODE /D_UNICODE /O2 RS485TestUI_CORRECTED.cpp /link user32.lib gdi32.lib comctl32.lib /SUBSYSTEM:WINDOWS"
    
    Write-Host "Executing build command..." -ForegroundColor Yellow
    cmd /c $buildCmd
    
    if (Test-Path "RS485TestUI_CORRECTED.exe") {
        Write-Host "✅ Build successful! Generated: RS485TestUI_CORRECTED.exe" -ForegroundColor Green
        
        # Copy to FinalOutput
        if (-not (Test-Path "FinalOutput")) {
            New-Item -ItemType Directory -Path "FinalOutput"
        }
        Copy-Item "RS485TestUI_CORRECTED.exe" "FinalOutput\"
        Write-Host "✅ Copied to FinalOutput directory" -ForegroundColor Green
        
        Write-Host ""
        Write-Host "This version uses the EXACT same pattern as the working RS485TestUI_Complete.exe:" -ForegroundColor Cyan
        Write-Host "✓ cbWndExtra = sizeof(RS485TestApp*)" -ForegroundColor White
        Write-Host "✓ SetWindowLongPtr(hWnd, 0, (LONG_PTR)pApp)" -ForegroundColor White
        Write-Host "✓ Direct control creation in WM_CREATE" -ForegroundColor White
        Write-Host "✓ Proper InitCommonControls() initialization" -ForegroundColor White
        Write-Host "✓ All RS485 test buttons and functionality" -ForegroundColor White
        Write-Host ""
        Write-Host "Ready to test: .\RS485TestUI_CORRECTED.exe" -ForegroundColor Green
        
    } else {
        Write-Host "❌ Build failed!" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Visual Studio not found!" -ForegroundColor Red
}

Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
