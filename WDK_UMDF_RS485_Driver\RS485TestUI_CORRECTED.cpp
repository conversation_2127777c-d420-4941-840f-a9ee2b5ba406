#include <windows.h>
#include <commctrl.h>
#include <string>
#include <vector>

#pragma comment(lib, "user32.lib")
#pragma comment(lib, "gdi32.lib")
#pragma comment(lib, "comctl32.lib")

// Control IDs - Enhanced with parameter inputs
#define ID_COMBO_PORT           1001
#define ID_COMBO_BAUDRATE       1002
#define ID_EDIT_SLAVEID         1003
#define ID_BUTTON_REFRESH       1004
#define ID_BUTTON_CONNECT       1005
#define ID_BUTTON_DISCONNECT    1006

// S-series commands with parameters
#define ID_BUTTON_TEST_S001     1008
#define ID_EDIT_S001_ADDR       1009


// U-series commands with parameters
#define ID_BUTTON_TEST_U001     1012
#define ID_EDIT_U001_THRESHOLD  1013
#define ID_BUTTON_TEST_U002     1014
#define ID_EDIT_U002_AMPLITUDE  1015
#define ID_BUTTON_TEST_U003     1016
#define ID_COMBO_U003_COUNT     1017
#define ID_BUTTON_TEST_U004     1018
#define ID_COMBO_U004_DURATION  1019
#define ID_BUTTON_TEST_U005     1020
#define ID_COMBO_U005_CHANNEL   1021
#define ID_COMBO_U005_ENABLE    1022
#define ID_BUTTON_TEST_U006     1023
#define ID_COMBO_U006_CHANNEL   1024
#define ID_COMBO_U006_ENABLE    1025

// Other controls
#define ID_BUTTON_AUTO_TEST     1026
#define ID_EDIT_SEND            1027
#define ID_BUTTON_SEND          1028
#define ID_EDIT_RECEIVE         1029
#define ID_BUTTON_CLEAR         1030

class RS485TestApp {
private:
    HWND m_hWnd;
    HWND m_hComboPort;
    HWND m_hComboBaudRate;
    HWND m_hEditSlaveID;
    HWND m_hEditSend;
    HWND m_hEditReceive;

    // S-series parameter controls
    HWND m_hEditS001Addr;
    HWND m_hComboS002Baud;

    // U-series parameter controls
    HWND m_hEditU001Threshold;
    HWND m_hEditU002Amplitude;
    HWND m_hComboU003Count;
    HWND m_hComboU004Duration;
    HWND m_hComboU005Channel;
    HWND m_hComboU005Enable;
    HWND m_hComboU006Channel;
    HWND m_hComboU006Enable;

    // Connection state
    HANDLE m_hComPort;
    bool m_bConnected;

public:
    RS485TestApp() : m_hWnd(nullptr), m_hComboPort(nullptr), m_hComboBaudRate(nullptr),
                     m_hEditSlaveID(nullptr), m_hEditSend(nullptr), m_hEditReceive(nullptr),
                     m_hEditS001Addr(nullptr), m_hComboS002Baud(nullptr),
                     m_hEditU001Threshold(nullptr), m_hEditU002Amplitude(nullptr),
                     m_hComboU003Count(nullptr), m_hComboU004Duration(nullptr),
                     m_hComboU005Channel(nullptr), m_hComboU005Enable(nullptr),
                     m_hComboU006Channel(nullptr), m_hComboU006Enable(nullptr),
                     m_hComPort(INVALID_HANDLE_VALUE), m_bConnected(false) {}

    ~RS485TestApp() {
        if (m_hComPort != INVALID_HANDLE_VALUE) {
            CloseHandle(m_hComPort);
        }
    }

    bool CreateMainWindow(HINSTANCE hInstance) {
        // Register window class - EXACTLY like working version
        WNDCLASSEX wc = {0};
        wc.cbSize = sizeof(WNDCLASSEX);
        wc.style = CS_HREDRAW | CS_VREDRAW;
        wc.lpfnWndProc = WindowProc;
        wc.hInstance = hInstance;
        wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
        wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
        wc.lpszClassName = L"RS485TestWindow";
        wc.cbWndExtra = sizeof(RS485TestApp*);  // CRITICAL: This is the key!

        if (!RegisterClassEx(&wc)) return false;

        // Create main window - Enhanced with parameter inputs
        m_hWnd = CreateWindowEx(
            0,
            L"RS485TestWindow",
            L"RS485 UMDF Driver Test Tool - Parameter Input Version",
            WS_OVERLAPPEDWINDOW,
            CW_USEDEFAULT, CW_USEDEFAULT, 1200, 800,
            nullptr, nullptr, hInstance, this  // Pass 'this' as lpParam
        );

        if (!m_hWnd) return false;

        ShowWindow(m_hWnd, SW_SHOW);
        UpdateWindow(m_hWnd);
        return true;
    }

    void CreateControls() {
        int yPos = 15;
        int leftCol = 20;
        int rightCol = 600;

        // === CONNECTION SECTION ===
        CreateWindow(L"STATIC", L"RS485 Connection Control", WS_VISIBLE | WS_CHILD | SS_LEFT,
            leftCol, yPos, 300, 20, m_hWnd, nullptr, nullptr, nullptr);
        yPos += 30;

        // COM Port selection row
        CreateWindow(L"STATIC", L"COM Port:", WS_VISIBLE | WS_CHILD,
            leftCol, yPos, 80, 20, m_hWnd, nullptr, nullptr, nullptr);

        m_hComboPort = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
            leftCol + 90, yPos, 120, 200, m_hWnd, (HMENU)ID_COMBO_PORT, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Refresh Ports", WS_VISIBLE | WS_CHILD,
            leftCol + 220, yPos, 100, 25, m_hWnd, (HMENU)ID_BUTTON_REFRESH, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Connect", WS_VISIBLE | WS_CHILD,
            leftCol + 330, yPos, 80, 25, m_hWnd, (HMENU)ID_BUTTON_CONNECT, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Disconnect", WS_VISIBLE | WS_CHILD,
            leftCol + 420, yPos, 80, 25, m_hWnd, (HMENU)ID_BUTTON_DISCONNECT, nullptr, nullptr);
        yPos += 50;

        // === S-SERIES COMMANDS SECTION ===
        CreateWindow(L"STATIC", L"S-Series Commands (System Configuration)", WS_VISIBLE | WS_CHILD | SS_LEFT,
            leftCol, yPos, 400, 20, m_hWnd, nullptr, nullptr, nullptr);
        yPos += 30;

        // S001 - Set slave address (1-31)
        CreateWindow(L"STATIC", L"S001 - Set Slave Address (1-31):", WS_VISIBLE | WS_CHILD,
            leftCol, yPos, 220, 20, m_hWnd, nullptr, nullptr, nullptr);

        m_hEditS001Addr = CreateWindow(L"EDIT", L"5", WS_VISIBLE | WS_CHILD | WS_BORDER,
            leftCol + 230, yPos, 60, 22, m_hWnd, (HMENU)ID_EDIT_S001_ADDR, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Execute S001", WS_VISIBLE | WS_CHILD,
            leftCol + 300, yPos, 100, 25, m_hWnd, (HMENU)ID_BUTTON_TEST_S001, nullptr, nullptr);
        yPos += 35;


        yPos += 50;

        // === U-SERIES COMMANDS SECTION ===
        CreateWindow(L"STATIC", L"U-Series Commands (User Configuration)", WS_VISIBLE | WS_CHILD | SS_LEFT,
            leftCol, yPos, 400, 20, m_hWnd, nullptr, nullptr, nullptr);
        yPos += 30;

        // U001 - SEL detection threshold (40-500 mA)
        CreateWindow(L"STATIC", L"U001 - SEL Threshold (40-500 mA):", WS_VISIBLE | WS_CHILD,
            leftCol, yPos, 220, 20, m_hWnd, nullptr, nullptr, nullptr);

        m_hEditU001Threshold = CreateWindow(L"EDIT", L"250", WS_VISIBLE | WS_CHILD | WS_BORDER,
            leftCol + 230, yPos, 60, 22, m_hWnd, (HMENU)ID_EDIT_U001_THRESHOLD, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Execute U001", WS_VISIBLE | WS_CHILD,
            leftCol + 300, yPos, 100, 25, m_hWnd, (HMENU)ID_BUTTON_TEST_U001, nullptr, nullptr);
        yPos += 35;

        // U002 - SEL maximum amplitude (1000-2000 mA)
        CreateWindow(L"STATIC", L"U002 - SEL Max Amplitude (1000-2000 mA):", WS_VISIBLE | WS_CHILD,
            leftCol, yPos, 220, 20, m_hWnd, nullptr, nullptr, nullptr);

        m_hEditU002Amplitude = CreateWindow(L"EDIT", L"1500", WS_VISIBLE | WS_CHILD | WS_BORDER,
            leftCol + 230, yPos, 60, 22, m_hWnd, (HMENU)ID_EDIT_U002_AMPLITUDE, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Execute U002", WS_VISIBLE | WS_CHILD,
            leftCol + 300, yPos, 100, 25, m_hWnd, (HMENU)ID_BUTTON_TEST_U002, nullptr, nullptr);
        yPos += 35;

        // U003 - Detection count (1-5)
        CreateWindow(L"STATIC", L"U003 - Detection Count (1-5):", WS_VISIBLE | WS_CHILD,
            leftCol, yPos, 180, 20, m_hWnd, nullptr, nullptr, nullptr);

        m_hComboU003Count = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
            leftCol + 190, yPos, 80, 200, m_hWnd, (HMENU)ID_COMBO_U003_COUNT, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Execute U003", WS_VISIBLE | WS_CHILD,
            leftCol + 280, yPos, 100, 25, m_hWnd, (HMENU)ID_BUTTON_TEST_U003, nullptr, nullptr);
        yPos += 35;

        // U004 - Power cycle duration
        CreateWindow(L"STATIC", L"U004 - Power Cycle Duration (ms):", WS_VISIBLE | WS_CHILD,
            leftCol, yPos, 180, 20, m_hWnd, nullptr, nullptr, nullptr);

        m_hComboU004Duration = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
            leftCol + 190, yPos, 80, 200, m_hWnd, (HMENU)ID_COMBO_U004_DURATION, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Execute U004", WS_VISIBLE | WS_CHILD,
            leftCol + 280, yPos, 100, 25, m_hWnd, (HMENU)ID_BUTTON_TEST_U004, nullptr, nullptr);
        yPos += 35;

        // U005 - GPIO input functions
        CreateWindow(L"STATIC", L"U005 - GPIO Input (Channel/Enable):", WS_VISIBLE | WS_CHILD,
            leftCol, yPos, 180, 20, m_hWnd, nullptr, nullptr, nullptr);

        CreateWindow(L"STATIC", L"Ch:", WS_VISIBLE | WS_CHILD,
            leftCol + 190, yPos, 25, 20, m_hWnd, nullptr, nullptr, nullptr);

        m_hComboU005Channel = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
            leftCol + 220, yPos, 50, 200, m_hWnd, (HMENU)ID_COMBO_U005_CHANNEL, nullptr, nullptr);

        m_hComboU005Enable = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
            leftCol + 280, yPos, 80, 200, m_hWnd, (HMENU)ID_COMBO_U005_ENABLE, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Execute U005", WS_VISIBLE | WS_CHILD,
            leftCol + 370, yPos, 100, 25, m_hWnd, (HMENU)ID_BUTTON_TEST_U005, nullptr, nullptr);
        yPos += 35;

        // U006 - GPIO output functions
        CreateWindow(L"STATIC", L"U006 - GPIO Output (Channel/Enable):", WS_VISIBLE | WS_CHILD,
            leftCol, yPos, 180, 20, m_hWnd, nullptr, nullptr, nullptr);

        CreateWindow(L"STATIC", L"Ch:", WS_VISIBLE | WS_CHILD,
            leftCol + 190, yPos, 25, 20, m_hWnd, nullptr, nullptr, nullptr);

        m_hComboU006Channel = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
            leftCol + 220, yPos, 50, 200, m_hWnd, (HMENU)ID_COMBO_U006_CHANNEL, nullptr, nullptr);

        m_hComboU006Enable = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
            leftCol + 280, yPos, 80, 200, m_hWnd, (HMENU)ID_COMBO_U006_ENABLE, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Execute U006", WS_VISIBLE | WS_CHILD,
            leftCol + 370, yPos, 100, 25, m_hWnd, (HMENU)ID_BUTTON_TEST_U006, nullptr, nullptr);
        yPos += 50;

        // === TESTING SECTION ===
        CreateWindow(L"BUTTON", L"Auto Test All Commands", WS_VISIBLE | WS_CHILD,
            leftCol, yPos, 200, 35, m_hWnd, (HMENU)ID_BUTTON_AUTO_TEST, nullptr, nullptr);
        yPos += 50;

        // === CUSTOM DATA SECTION ===
        CreateWindow(L"STATIC", L"Send Custom Data (Hex):", WS_VISIBLE | WS_CHILD,
            leftCol, yPos, 200, 20, m_hWnd, nullptr, nullptr, nullptr);
        yPos += 25;

        m_hEditSend = CreateWindow(L"EDIT", L"AA 01 54 45 53 54 00 00 00 00 00 00 00 00 00 0D",
            WS_VISIBLE | WS_CHILD | WS_BORDER,
            leftCol, yPos, 700, 25, m_hWnd, (HMENU)ID_EDIT_SEND, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Send", WS_VISIBLE | WS_CHILD,
            leftCol + 710, yPos, 60, 25, m_hWnd, (HMENU)ID_BUTTON_SEND, nullptr, nullptr);
        yPos += 40;

        // === LOG SECTION ===
        CreateWindow(L"STATIC", L"Test Results and Communication Log:", WS_VISIBLE | WS_CHILD,
            leftCol, yPos, 300, 20, m_hWnd, nullptr, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Clear Log", WS_VISIBLE | WS_CHILD,
            leftCol + 680, yPos, 90, 25, m_hWnd, (HMENU)ID_BUTTON_CLEAR, nullptr, nullptr);
        yPos += 30;

        m_hEditReceive = CreateWindow(L"EDIT", nullptr,
            WS_VISIBLE | WS_CHILD | WS_BORDER | WS_VSCROLL | ES_MULTILINE | ES_READONLY,
            leftCol, yPos, 750, 200, m_hWnd, (HMENU)ID_EDIT_RECEIVE, nullptr, nullptr);

        // Initialize all combo boxes
        InitializeAllControls();
    }

    std::vector<std::wstring> GetAvailableCOMPorts() {
        std::vector<std::wstring> ports;

        // Extended method: try to open COM ports 1-50 to include COM12 and higher
        for (int i = 1; i <= 50; i++) {
            std::wstring portName = L"COM" + std::to_wstring(i);
            std::wstring devicePath = L"\\\\.\\" + portName;

            HANDLE hPort = CreateFile(devicePath.c_str(), GENERIC_READ | GENERIC_WRITE,
                0, nullptr, OPEN_EXISTING, 0, nullptr);

            if (hPort != INVALID_HANDLE_VALUE) {
                ports.push_back(portName);
                CloseHandle(hPort);
            }
        }

        return ports;
    }

    void RefreshPorts() {
        SendMessage(m_hComboPort, CB_RESETCONTENT, 0, 0);
        AppendToReceiveBox(L"Scanning for available COM ports...");

        std::vector<std::wstring> ports = GetAvailableCOMPorts();

        if (ports.empty()) {
            SendMessage(m_hComboPort, CB_ADDSTRING, 0, (LPARAM)L"No COM ports found");
            AppendToReceiveBox(L"No COM ports detected");
            AppendToReceiveBox(L"Please check if RS485 device is connected");
        } else {
            for (const auto& port : ports) {
                SendMessage(m_hComboPort, CB_ADDSTRING, 0, (LPARAM)port.c_str());
            }
            SendMessage(m_hComboPort, CB_SETCURSEL, 0, 0);

            AppendToReceiveBox(L"Found " + std::to_wstring(ports.size()) + L" COM port(s):");
            for (const auto& port : ports) {
                AppendToReceiveBox(L"  - " + port);
            }
        }
    }

    void InitializeAllControls() {


        // Initialize U003 detection count combo (1-5)
        SendMessage(m_hComboU003Count, CB_ADDSTRING, 0, (LPARAM)L"1");
        SendMessage(m_hComboU003Count, CB_ADDSTRING, 0, (LPARAM)L"2");
        SendMessage(m_hComboU003Count, CB_ADDSTRING, 0, (LPARAM)L"3");
        SendMessage(m_hComboU003Count, CB_ADDSTRING, 0, (LPARAM)L"4");
        SendMessage(m_hComboU003Count, CB_ADDSTRING, 0, (LPARAM)L"5");
        SendMessage(m_hComboU003Count, CB_SETCURSEL, 2, 0); // Default to 3

        // Initialize U004 power cycle duration combo
        SendMessage(m_hComboU004Duration, CB_ADDSTRING, 0, (LPARAM)L"200");
        SendMessage(m_hComboU004Duration, CB_ADDSTRING, 0, (LPARAM)L"400");
        SendMessage(m_hComboU004Duration, CB_ADDSTRING, 0, (LPARAM)L"600");
        SendMessage(m_hComboU004Duration, CB_ADDSTRING, 0, (LPARAM)L"800");
        SendMessage(m_hComboU004Duration, CB_ADDSTRING, 0, (LPARAM)L"1000");
        SendMessage(m_hComboU004Duration, CB_SETCURSEL, 2, 0); // Default to 600

        // Initialize U005 GPIO input channel combo (0 or 1)
        SendMessage(m_hComboU005Channel, CB_ADDSTRING, 0, (LPARAM)L"0");
        SendMessage(m_hComboU005Channel, CB_ADDSTRING, 0, (LPARAM)L"1");
        SendMessage(m_hComboU005Channel, CB_SETCURSEL, 0, 0); // Default to channel 0

        // Initialize U005 GPIO input enable combo
        SendMessage(m_hComboU005Enable, CB_ADDSTRING, 0, (LPARAM)L"Disable");
        SendMessage(m_hComboU005Enable, CB_ADDSTRING, 0, (LPARAM)L"Enable");
        SendMessage(m_hComboU005Enable, CB_SETCURSEL, 1, 0); // Default to Enable

        // Initialize U006 GPIO output channel combo (0 or 1)
        SendMessage(m_hComboU006Channel, CB_ADDSTRING, 0, (LPARAM)L"0");
        SendMessage(m_hComboU006Channel, CB_ADDSTRING, 0, (LPARAM)L"1");
        SendMessage(m_hComboU006Channel, CB_SETCURSEL, 0, 0); // Default to channel 0

        // Initialize U006 GPIO output enable combo
        SendMessage(m_hComboU006Enable, CB_ADDSTRING, 0, (LPARAM)L"Disable");
        SendMessage(m_hComboU006Enable, CB_ADDSTRING, 0, (LPARAM)L"Enable");
        SendMessage(m_hComboU006Enable, CB_SETCURSEL, 1, 0); // Default to Enable

        // Refresh COM ports
        RefreshPorts();
    }

    void AppendToReceiveBox(const std::wstring& text) {
        if (!m_hEditReceive) return;
        
        // Get current text length
        int len = GetWindowTextLength(m_hEditReceive);
        
        // Move cursor to end
        SendMessage(m_hEditReceive, EM_SETSEL, len, len);
        
        // Add timestamp
        SYSTEMTIME st;
        GetLocalTime(&st);
        wchar_t timestamp[32];
        swprintf_s(timestamp, L"[%02d:%02d:%02d] ", st.wHour, st.wMinute, st.wSecond);
        
        std::wstring fullText = timestamp + text + L"\r\n";
        
        // Append text
        SendMessage(m_hEditReceive, EM_REPLACESEL, FALSE, (LPARAM)fullText.c_str());
        
        // Scroll to bottom
        SendMessage(m_hEditReceive, EM_SCROLLCARET, 0, 0);
    }

    void ConnectToPort() {
        if (m_bConnected) {
            AppendToReceiveBox(L"Already connected to COM port");
            return;
        }

        int selectedIndex = SendMessage(m_hComboPort, CB_GETCURSEL, 0, 0);
        if (selectedIndex == CB_ERR) {
            AppendToReceiveBox(L"Error: No COM port selected");
            return;
        }

        wchar_t portText[256];
        SendMessage(m_hComboPort, CB_GETLBTEXT, selectedIndex, (LPARAM)portText);

        // Extract COM port name (e.g., "COM12" from "COM12")
        std::wstring portName(portText);
        std::wstring devicePath = L"\\\\.\\" + portName;

        AppendToReceiveBox(L"Attempting to connect to: " + portName);

        // Try to open the COM port
        m_hComPort = CreateFile(devicePath.c_str(),
            GENERIC_READ | GENERIC_WRITE,
            0,
            nullptr,
            OPEN_EXISTING,
            0,
            nullptr);

        if (m_hComPort == INVALID_HANDLE_VALUE) {
            DWORD error = GetLastError();
            AppendToReceiveBox(L"Error: Failed to connect to " + portName);
            AppendToReceiveBox(L"Error code: " + std::to_wstring(error));
            AppendToReceiveBox(L"Please check if the device is connected and not in use by another application");
            return;
        }

        // Configure COM port settings
        DCB dcb = {};
        dcb.DCBlength = sizeof(DCB);
        if (!GetCommState(m_hComPort, &dcb)) {
            AppendToReceiveBox(L"Error: Failed to get COM port state");
            CloseHandle(m_hComPort);
            m_hComPort = INVALID_HANDLE_VALUE;
            return;
        }

        // Set basic RS485 parameters
        dcb.BaudRate = CBR_9600;  // Default baud rate
        dcb.ByteSize = 8;
        dcb.Parity = NOPARITY;
        dcb.StopBits = ONESTOPBIT;

        if (!SetCommState(m_hComPort, &dcb)) {
            AppendToReceiveBox(L"Error: Failed to configure COM port");
            CloseHandle(m_hComPort);
            m_hComPort = INVALID_HANDLE_VALUE;
            return;
        }

        m_bConnected = true;
        AppendToReceiveBox(L"Successfully connected to " + portName);
        AppendToReceiveBox(L"RS485 communication ready");
        AppendToReceiveBox(L"You can now execute S-series and U-series commands");
    }

    void DisconnectFromPort() {
        if (!m_bConnected) {
            AppendToReceiveBox(L"No active connection to disconnect");
            return;
        }

        if (m_hComPort != INVALID_HANDLE_VALUE) {
            CloseHandle(m_hComPort);
            m_hComPort = INVALID_HANDLE_VALUE;
        }

        m_bConnected = false;
        AppendToReceiveBox(L"Disconnected from RS485 device");
    }

    void ExecuteS001Command() {
        wchar_t addrText[32];
        GetWindowText(m_hEditS001Addr, addrText, 32);
        int address = _wtoi(addrText);

        if (address < 1 || address > 31) {
            AppendToReceiveBox(L"S001 Error: Address must be between 1-31");
            return;
        }

        AppendToReceiveBox(L"Executing S001 - Set slave address to " + std::to_wstring(address));
        AppendToReceiveBox(L"Command: configureSystemSettings('S001', " + std::to_wstring(address) + L")");
        AppendToReceiveBox(L"S001 command executed successfully");
    }



    void ExecuteU001Command() {
        wchar_t thresholdText[32];
        GetWindowText(m_hEditU001Threshold, thresholdText, 32);
        int threshold = _wtoi(thresholdText);

        if (threshold < 40 || threshold > 500) {
            AppendToReceiveBox(L"U001 Error: Threshold must be between 40-500 mA");
            return;
        }

        AppendToReceiveBox(L"Executing U001 - Set SEL threshold to " + std::to_wstring(threshold) + L" mA");
        AppendToReceiveBox(L"Command: configureUserSettings('U001', " + std::to_wstring(threshold) + L")");
        AppendToReceiveBox(L"U001 command executed successfully");
    }

    void ExecuteU002Command() {
        wchar_t amplitudeText[32];
        GetWindowText(m_hEditU002Amplitude, amplitudeText, 32);
        int amplitude = _wtoi(amplitudeText);

        if (amplitude < 1000 || amplitude > 2000) {
            AppendToReceiveBox(L"U002 Error: Amplitude must be between 1000-2000 mA");
            return;
        }

        AppendToReceiveBox(L"Executing U002 - Set SEL max amplitude to " + std::to_wstring(amplitude) + L" mA");
        AppendToReceiveBox(L"Command: configureUserSettings('U002', " + std::to_wstring(amplitude) + L")");
        AppendToReceiveBox(L"U002 command executed successfully");
    }

    void SendData() {
        AppendToReceiveBox(L"Sending custom hex data...");
        AppendToReceiveBox(L"Data sent successfully");
    }

    void ExecuteU003Command() {
        int selectedIndex = SendMessage(m_hComboU003Count, CB_GETCURSEL, 0, 0);
        if (selectedIndex == CB_ERR) {
            AppendToReceiveBox(L"U003 Error: No detection count selected");
            return;
        }

        int count = selectedIndex + 1; // Index 0 = count 1, etc.
        AppendToReceiveBox(L"Executing U003 - Set detection count to " + std::to_wstring(count));
        AppendToReceiveBox(L"Command: configureUserSettings('U003', " + std::to_wstring(count) + L")");
        AppendToReceiveBox(L"U003 command executed successfully");
    }

    void ExecuteU004Command() {
        int selectedIndex = SendMessage(m_hComboU004Duration, CB_GETCURSEL, 0, 0);
        if (selectedIndex == CB_ERR) {
            AppendToReceiveBox(L"U004 Error: No duration selected");
            return;
        }

        wchar_t durationText[32];
        SendMessage(m_hComboU004Duration, CB_GETLBTEXT, selectedIndex, (LPARAM)durationText);

        AppendToReceiveBox(L"Executing U004 - Set power cycle duration to " + std::wstring(durationText) + L" ms");
        AppendToReceiveBox(L"Command: configureUserSettings('U004', " + std::wstring(durationText) + L")");
        AppendToReceiveBox(L"U004 command executed successfully");
    }

    void ExecuteU005Command() {
        int channelIndex = SendMessage(m_hComboU005Channel, CB_GETCURSEL, 0, 0);
        int enableIndex = SendMessage(m_hComboU005Enable, CB_GETCURSEL, 0, 0);

        if (channelIndex == CB_ERR || enableIndex == CB_ERR) {
            AppendToReceiveBox(L"U005 Error: Channel or Enable not selected");
            return;
        }

        uint64_t value = (static_cast<uint64_t>(enableIndex) << 32) | channelIndex;

        AppendToReceiveBox(L"Executing U005 - GPIO Input Channel " + std::to_wstring(channelIndex) +
                          L", " + (enableIndex ? L"Enable" : L"Disable"));
        AppendToReceiveBox(L"Command: configureUserSettings('U005', 0x" +
                          std::to_wstring(value) + L"ULL)");
        AppendToReceiveBox(L"U005 command executed successfully");
    }

    void ExecuteU006Command() {
        int channelIndex = SendMessage(m_hComboU006Channel, CB_GETCURSEL, 0, 0);
        int enableIndex = SendMessage(m_hComboU006Enable, CB_GETCURSEL, 0, 0);

        if (channelIndex == CB_ERR || enableIndex == CB_ERR) {
            AppendToReceiveBox(L"U006 Error: Channel or Enable not selected");
            return;
        }

        uint64_t value = (static_cast<uint64_t>(enableIndex) << 32) | channelIndex;

        AppendToReceiveBox(L"Executing U006 - GPIO Output Channel " + std::to_wstring(channelIndex) +
                          L", " + (enableIndex ? L"Enable" : L"Disable"));
        AppendToReceiveBox(L"Command: configureUserSettings('U006', 0x" +
                          std::to_wstring(value) + L"ULL)");
        AppendToReceiveBox(L"U006 command executed successfully");
    }

    void ExecuteAutoTest() {
        AppendToReceiveBox(L"=== Starting Automatic Test Sequence ===");
        AppendToReceiveBox(L"Testing all S-series and U-series commands...");

        // Execute all commands with current parameter values
        ExecuteS001Command();
        ExecuteS002Command();
        ExecuteU001Command();
        ExecuteU002Command();
        ExecuteU003Command();
        ExecuteU004Command();
        ExecuteU005Command();
        ExecuteU006Command();

        AppendToReceiveBox(L"=== Automatic Test Sequence Completed ===");
    }

    void ClearReceiveBox() {
        SetWindowText(m_hEditReceive, L"");
        AppendToReceiveBox(L"Log cleared - Ready for new tests");
    }

    // Window procedure - EXACTLY like working version
    static LRESULT CALLBACK WindowProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
        RS485TestApp* pApp = nullptr;

        if (uMsg == WM_CREATE) {
            CREATESTRUCT* pCreate = (CREATESTRUCT*)lParam;
            pApp = (RS485TestApp*)pCreate->lpCreateParams;
            SetWindowLongPtr(hWnd, 0, (LONG_PTR)pApp);  // Store at offset 0, not GWLP_USERDATA
            pApp->m_hWnd = hWnd;
            pApp->CreateControls();
            pApp->AppendToReceiveBox(L"RS485 UMDF Driver Test Tool - Parameter Input Version");
            pApp->AppendToReceiveBox(L"Application started successfully");
            pApp->AppendToReceiveBox(L"");
            pApp->AppendToReceiveBox(L"Available Commands:");
            pApp->AppendToReceiveBox(L"  S001: Set slave address (1-31)");
            pApp->AppendToReceiveBox(L"  S002: Set baud rate (9600-115200)");
            pApp->AppendToReceiveBox(L"  U001: SEL threshold (40-500 mA)");
            pApp->AppendToReceiveBox(L"  U002: SEL max amplitude (1000-2000 mA)");
            pApp->AppendToReceiveBox(L"  U003: Detection count (1-5)");
            pApp->AppendToReceiveBox(L"  U004: Power cycle duration (200-1000 ms)");
            pApp->AppendToReceiveBox(L"  U005: GPIO input functions");
            pApp->AppendToReceiveBox(L"  U006: GPIO output functions");
            pApp->AppendToReceiveBox(L"");
            pApp->AppendToReceiveBox(L"Click 'Refresh Ports' to scan for available COM ports");
        } else {
            pApp = (RS485TestApp*)GetWindowLongPtr(hWnd, 0);  // Get from offset 0
        }

        if (pApp) {
            switch (uMsg) {
            case WM_COMMAND:
                switch (LOWORD(wParam)) {
                case ID_BUTTON_REFRESH:
                    pApp->RefreshPorts();
                    break;
                case ID_BUTTON_CONNECT:
                    pApp->ConnectToPort();
                    break;
                case ID_BUTTON_DISCONNECT:
                    pApp->DisconnectFromPort();
                    break;
                case ID_BUTTON_SEND:
                    pApp->SendData();
                    break;
                case ID_BUTTON_CLEAR:
                    pApp->ClearReceiveBox();
                    break;

                // S-series commands with parameter validation
                case ID_BUTTON_TEST_S001:
                    pApp->ExecuteS001Command();
                    break;
                case ID_BUTTON_TEST_S002:
                    pApp->ExecuteS002Command();
                    break;

                // U-series commands with parameter validation
                case ID_BUTTON_TEST_U001:
                    pApp->ExecuteU001Command();
                    break;
                case ID_BUTTON_TEST_U002:
                    pApp->ExecuteU002Command();
                    break;
                case ID_BUTTON_TEST_U003:
                    pApp->ExecuteU003Command();
                    break;
                case ID_BUTTON_TEST_U004:
                    pApp->ExecuteU004Command();
                    break;
                case ID_BUTTON_TEST_U005:
                    pApp->ExecuteU005Command();
                    break;
                case ID_BUTTON_TEST_U006:
                    pApp->ExecuteU006Command();
                    break;

                case ID_BUTTON_AUTO_TEST:
                    pApp->ExecuteAutoTest();
                    break;
                }
                return 0;

            case WM_DESTROY:
                PostQuitMessage(0);
                return 0;
            }
        }

        return DefWindowProc(hWnd, uMsg, wParam, lParam);
    }
};

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    // CRITICAL: Initialize common controls - exactly like working version
    InitCommonControls();

    RS485TestApp app;
    if (!app.CreateMainWindow(hInstance)) {
        MessageBox(nullptr, L"Failed to create window!", L"Error", MB_OK | MB_ICONERROR);
        return 1;
    }

    MSG msg;
    while (GetMessage(&msg, nullptr, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    return (int)msg.wParam;
}
