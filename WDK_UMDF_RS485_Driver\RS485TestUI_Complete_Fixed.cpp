#include <windows.h>
#include <commctrl.h>
#include <setupapi.h>
#include <devguid.h>
#include <regstr.h>
#include <string>
#include <vector>
#include <sstream>
#include <iomanip>
#include <thread>
#include <chrono>
#include <algorithm>
#include <cstring>

#pragma comment(lib, "user32.lib")
#pragma comment(lib, "gdi32.lib")
#pragma comment(lib, "comctl32.lib")
#pragma comment(lib, "setupapi.lib")
#pragma comment(lib, "advapi32.lib")

// Window dimensions
#define WINDOW_WIDTH 1200
#define WINDOW_HEIGHT 900

// Control IDs
#define ID_BUTTON_REFRESH_PORTS 1010
#define ID_BUTTON_CONNECT       1011
#define ID_BUTTON_DISCONNECT    1012
#define ID_STATIC_STATUS        1013
#define ID_PROGRESS_BAR         1014
#define ID_COMBO_PORT_SELECT    1015

// S-Series Commands
#define ID_STATIC_S001          1020
#define ID_EDIT_S001            1021
#define ID_BUTTON_S001          1022
#define ID_STATIC_S002          1023
#define ID_COMBO_S002           1024
#define ID_BUTTON_S002          1025

// U-Series Commands
#define ID_STATIC_U001          1030
#define ID_EDIT_U001            1031
#define ID_BUTTON_U001          1032
#define ID_STATIC_U002          1033
#define ID_EDIT_U002            1034
#define ID_BUTTON_U002          1035
#define ID_STATIC_U003          1036
#define ID_COMBO_U003           1037
#define ID_BUTTON_U003          1038
#define ID_STATIC_U004          1039
#define ID_COMBO_U004           1040
#define ID_BUTTON_U004          1041
#define ID_STATIC_U005          1042
#define ID_COMBO_U005_CH        1043
#define ID_COMBO_U005_EN        1044
#define ID_BUTTON_U005          1045
#define ID_STATIC_U006          1046
#define ID_COMBO_U006_CH        1047
#define ID_COMBO_U006_EN        1048
#define ID_BUTTON_U006          1049

// Data display
#define ID_EDIT_SEND            1050
#define ID_BUTTON_SEND          1051
#define ID_EDIT_RECEIVE         1052
#define ID_BUTTON_CLEAR         1053
#define ID_BUTTON_TEST_ALL      1054

// Global variables
HINSTANCE g_hInstance = nullptr;

class RS485TestApplication {
private:
    HWND m_hMainWindow;
    HWND m_hStatusText;
    HWND m_hProgressBar;
    HWND m_hSendEdit;
    HWND m_hReceiveEdit;
    HWND m_hPortCombo;
    
    // S-Series controls
    HWND m_hS001Edit;
    HWND m_hS002Combo;
    
    // U-Series controls
    HWND m_hU001Edit;
    HWND m_hU002Edit;
    HWND m_hU003Combo;
    HWND m_hU004Combo;
    HWND m_hU005ChannelCombo;
    HWND m_hU005EnableCombo;
    HWND m_hU006ChannelCombo;
    HWND m_hU006EnableCombo;
    
    HANDLE m_hComPort;
    bool m_bConnected;
    std::wstring m_currentPort;
    std::vector<std::wstring> m_availablePorts;

public:
    RS485TestApplication() : 
        m_hMainWindow(nullptr),
        m_hStatusText(nullptr),
        m_hProgressBar(nullptr),
        m_hSendEdit(nullptr),
        m_hReceiveEdit(nullptr),
        m_hPortCombo(nullptr),
        m_hS001Edit(nullptr),
        m_hS002Combo(nullptr),
        m_hU001Edit(nullptr),
        m_hU002Edit(nullptr),
        m_hU003Combo(nullptr),
        m_hU004Combo(nullptr),
        m_hU005ChannelCombo(nullptr),
        m_hU005EnableCombo(nullptr),
        m_hU006ChannelCombo(nullptr),
        m_hU006EnableCombo(nullptr),
        m_hComPort(INVALID_HANDLE_VALUE),
        m_bConnected(false) {}
    
    ~RS485TestApplication() {
        if (m_hComPort != INVALID_HANDLE_VALUE) {
            CloseHandle(m_hComPort);
        }
    }

    bool Initialize(HINSTANCE hInstance) {
        g_hInstance = hInstance;
        
        // Initialize common controls
        INITCOMMONCONTROLSEX icex;
        icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
        icex.dwICC = ICC_PROGRESS_CLASS | ICC_LISTVIEW_CLASSES;
        if (!InitCommonControlsEx(&icex)) {
            MessageBox(nullptr, L"Failed to initialize common controls", L"Error", MB_OK | MB_ICONERROR);
            return false;
        }

        // Register window class
        WNDCLASSEX wc = {};
        wc.cbSize = sizeof(WNDCLASSEX);
        wc.style = CS_HREDRAW | CS_VREDRAW;
        wc.lpfnWndProc = StaticWindowProc;
        wc.hInstance = hInstance;
        wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
        wc.hbrBackground = (HBRUSH)(COLOR_BTNFACE + 1);
        wc.lpszClassName = L"RS485TestApplication";
        wc.hIcon = LoadIcon(nullptr, IDI_APPLICATION);
        wc.hIconSm = LoadIcon(nullptr, IDI_APPLICATION);

        if (!RegisterClassEx(&wc)) {
            DWORD error = GetLastError();
            std::wstring errorMsg = L"Failed to register window class. Error: " + std::to_wstring(error);
            MessageBox(nullptr, errorMsg.c_str(), L"Error", MB_OK | MB_ICONERROR);
            return false;
        }

        // Create main window
        m_hMainWindow = CreateWindowEx(
            0,
            L"RS485TestApplication",
            L"RS485 Driver Test Application - Complete Version",
            WS_OVERLAPPEDWINDOW,
            CW_USEDEFAULT, CW_USEDEFAULT,
            WINDOW_WIDTH, WINDOW_HEIGHT,
            nullptr, nullptr, hInstance, this
        );

        if (!m_hMainWindow) {
            DWORD error = GetLastError();
            std::wstring errorMsg = L"Failed to create main window. Error: " + std::to_wstring(error);
            MessageBox(nullptr, errorMsg.c_str(), L"Error", MB_OK | MB_ICONERROR);
            return false;
        }

        ShowWindow(m_hMainWindow, SW_SHOW);
        UpdateWindow(m_hMainWindow);
        
        return true;
    }

    static LRESULT CALLBACK StaticWindowProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
        RS485TestApplication* pApp = nullptr;

        if (uMsg == WM_NCCREATE) {
            CREATESTRUCT* pCreate = reinterpret_cast<CREATESTRUCT*>(lParam);
            pApp = reinterpret_cast<RS485TestApplication*>(pCreate->lpCreateParams);
            SetWindowLongPtr(hWnd, GWLP_USERDATA, reinterpret_cast<LONG_PTR>(pApp));
        } else {
            pApp = reinterpret_cast<RS485TestApplication*>(GetWindowLongPtr(hWnd, GWLP_USERDATA));
        }

        if (pApp) {
            return pApp->WindowProc(hWnd, uMsg, wParam, lParam);
        }

        return DefWindowProc(hWnd, uMsg, wParam, lParam);
    }

    LRESULT WindowProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
        switch (uMsg) {
        case WM_CREATE:
            CreateControls();
            // Post a message to initialize after controls are created
            PostMessage(hWnd, WM_USER + 1, 0, 0);
            return 0;

        case WM_USER + 1:
            // Initialize after all controls are created
            LogMessage(L"RS485 Test Application initialized successfully");
            LogMessage(L"Select a COM port and click 'Connect' to begin testing");
            return 0;

        case WM_COMMAND:
            HandleCommand(LOWORD(wParam));
            return 0;

        case WM_DESTROY:
            DisconnectFromPort();
            PostQuitMessage(0);
            return 0;

        case WM_PAINT: {
            PAINTSTRUCT ps;
            HDC hdc = BeginPaint(hWnd, &ps);
            EndPaint(hWnd, &ps);
            return 0;
        }
        }

        return DefWindowProc(hWnd, uMsg, wParam, lParam);
    }

    void CreateControls() {
        if (!m_hMainWindow) return;

        int yPos = 20;

        // Connection section
        CreateWindow(L"STATIC", L"RS485 Connection Control",
            WS_VISIBLE | WS_CHILD | SS_LEFT,
            20, yPos, 300, 20, m_hMainWindow, nullptr, g_hInstance, nullptr);
        yPos += 25;

        // Port selection
        CreateWindow(L"STATIC", L"Select Port:", WS_VISIBLE | WS_CHILD,
            20, yPos, 80, 20, m_hMainWindow, nullptr, g_hInstance, nullptr);

        m_hPortCombo = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST | WS_VSCROLL,
            110, yPos, 150, 200, m_hMainWindow, (HMENU)ID_COMBO_PORT_SELECT, g_hInstance, nullptr);

        CreateWindow(L"BUTTON", L"Refresh Ports", WS_VISIBLE | WS_CHILD,
            270, yPos, 100, 25, m_hMainWindow, (HMENU)ID_BUTTON_REFRESH_PORTS, g_hInstance, nullptr);

        CreateWindow(L"BUTTON", L"Connect", WS_VISIBLE | WS_CHILD,
            380, yPos, 80, 25, m_hMainWindow, (HMENU)ID_BUTTON_CONNECT, g_hInstance, nullptr);

        CreateWindow(L"BUTTON", L"Disconnect", WS_VISIBLE | WS_CHILD,
            470, yPos, 80, 25, m_hMainWindow, (HMENU)ID_BUTTON_DISCONNECT, g_hInstance, nullptr);
        yPos += 35;

        // Status display
        CreateWindow(L"STATIC", L"Status:", WS_VISIBLE | WS_CHILD,
            20, yPos, 60, 20, m_hMainWindow, nullptr, g_hInstance, nullptr);

        m_hStatusText = CreateWindow(L"STATIC", L"Disconnected",
            WS_VISIBLE | WS_CHILD | SS_SUNKEN | SS_LEFT,
            90, yPos, 400, 20, m_hMainWindow, (HMENU)ID_STATIC_STATUS, g_hInstance, nullptr);

        m_hProgressBar = CreateWindow(PROGRESS_CLASS, nullptr, WS_VISIBLE | WS_CHILD,
            500, yPos, 200, 20, m_hMainWindow, (HMENU)ID_PROGRESS_BAR, g_hInstance, nullptr);
        SendMessage(m_hProgressBar, PBM_SETRANGE, 0, MAKELPARAM(0, 100));
        yPos += 40;

        // Create command sections
        CreateSystemCommands(yPos);
        CreateUserCommands(yPos + 120);
        CreateDataSection(yPos + 360);

        // Refresh ports on startup
        RefreshPortList();
    }

    void CreateSystemCommands(int startY) {
        // S-Series Commands Section
        CreateWindow(L"STATIC", L"System Configuration Commands (S-Series)",
            WS_VISIBLE | WS_CHILD | SS_LEFT,
            20, startY, 400, 20, m_hMainWindow, nullptr, g_hInstance, nullptr);
        startY += 30;

        // S001: Slave Address (1-31)
        CreateWindow(L"STATIC", L"S001 - Set RS485 slave address (1-31):", WS_VISIBLE | WS_CHILD,
            20, startY, 250, 20, m_hMainWindow, (HMENU)ID_STATIC_S001, g_hInstance, nullptr);

        m_hS001Edit = CreateWindow(L"EDIT", L"5", WS_VISIBLE | WS_CHILD | WS_BORDER,
            280, startY, 60, 25, m_hMainWindow, (HMENU)ID_EDIT_S001, g_hInstance, nullptr);

        CreateWindow(L"BUTTON", L"Send S001", WS_VISIBLE | WS_CHILD,
            350, startY, 80, 25, m_hMainWindow, (HMENU)ID_BUTTON_S001, g_hInstance, nullptr);
        startY += 35;

        // S002: Baud Rate
        CreateWindow(L"STATIC", L"S002 - Set baud rate:", WS_VISIBLE | WS_CHILD,
            20, startY, 250, 20, m_hMainWindow, (HMENU)ID_STATIC_S002, g_hInstance, nullptr);

        m_hS002Combo = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST | WS_VSCROLL,
            280, startY, 100, 200, m_hMainWindow, (HMENU)ID_COMBO_S002, g_hInstance, nullptr);

        // Add baud rate options
        SendMessage(m_hS002Combo, CB_ADDSTRING, 0, (LPARAM)L"9600");
        SendMessage(m_hS002Combo, CB_ADDSTRING, 0, (LPARAM)L"19200");
        SendMessage(m_hS002Combo, CB_ADDSTRING, 0, (LPARAM)L"38400");
        SendMessage(m_hS002Combo, CB_ADDSTRING, 0, (LPARAM)L"57600");
        SendMessage(m_hS002Combo, CB_ADDSTRING, 0, (LPARAM)L"115200");
        SendMessage(m_hS002Combo, CB_SETCURSEL, 4, 0); // Default to 115200

        CreateWindow(L"BUTTON", L"Send S002", WS_VISIBLE | WS_CHILD,
            390, startY, 80, 25, m_hMainWindow, (HMENU)ID_BUTTON_S002, g_hInstance, nullptr);
    }

    void CreateUserCommands(int startY) {
        // U-Series Commands Section
        CreateWindow(L"STATIC", L"User Configuration Commands (U-Series)",
            WS_VISIBLE | WS_CHILD | SS_LEFT,
            20, startY, 400, 20, m_hMainWindow, nullptr, g_hInstance, nullptr);
        startY += 30;

        // U001: SEL Threshold (40-500 mA)
        CreateWindow(L"STATIC", L"U001 - SEL detection threshold (40-500 mA):", WS_VISIBLE | WS_CHILD,
            20, startY, 250, 20, m_hMainWindow, (HMENU)ID_STATIC_U001, g_hInstance, nullptr);

        m_hU001Edit = CreateWindow(L"EDIT", L"250", WS_VISIBLE | WS_CHILD | WS_BORDER,
            280, startY, 60, 25, m_hMainWindow, (HMENU)ID_EDIT_U001, g_hInstance, nullptr);

        CreateWindow(L"BUTTON", L"Send U001", WS_VISIBLE | WS_CHILD,
            350, startY, 80, 25, m_hMainWindow, (HMENU)ID_BUTTON_U001, g_hInstance, nullptr);
        startY += 35;

        // U002: Max Amplitude (1000-2000 mA)
        CreateWindow(L"STATIC", L"U002 - SEL max amplitude threshold (1000-2000 mA):", WS_VISIBLE | WS_CHILD,
            20, startY, 250, 20, m_hMainWindow, (HMENU)ID_STATIC_U002, g_hInstance, nullptr);

        m_hU002Edit = CreateWindow(L"EDIT", L"1500", WS_VISIBLE | WS_CHILD | WS_BORDER,
            280, startY, 60, 25, m_hMainWindow, (HMENU)ID_EDIT_U002, g_hInstance, nullptr);

        CreateWindow(L"BUTTON", L"Send U002", WS_VISIBLE | WS_CHILD,
            350, startY, 80, 25, m_hMainWindow, (HMENU)ID_BUTTON_U002, g_hInstance, nullptr);
        startY += 35;

        // U003: Detection Count (1-5)
        CreateWindow(L"STATIC", L"U003 - Number of SEL detections before power cycle (1-5):", WS_VISIBLE | WS_CHILD,
            20, startY, 250, 20, m_hMainWindow, (HMENU)ID_STATIC_U003, g_hInstance, nullptr);

        m_hU003Combo = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
            280, startY, 60, 150, m_hMainWindow, (HMENU)ID_COMBO_U003, g_hInstance, nullptr);

        for (int i = 1; i <= 5; i++) {
            wchar_t buffer[10];
            swprintf_s(buffer, L"%d", i);
            SendMessage(m_hU003Combo, CB_ADDSTRING, 0, (LPARAM)buffer);
        }
        SendMessage(m_hU003Combo, CB_SETCURSEL, 2, 0); // Default to 3

        CreateWindow(L"BUTTON", L"Send U003", WS_VISIBLE | WS_CHILD,
            350, startY, 80, 25, m_hMainWindow, (HMENU)ID_BUTTON_U003, g_hInstance, nullptr);
        startY += 35;

        // Continue with remaining U-series commands...
        CreateRemainingUserCommands(startY);
    }

    void CreateRemainingUserCommands(int startY) {
        // U004: Power Cycle Duration
        CreateWindow(L"STATIC", L"U004 - Power cycle duration (ms):", WS_VISIBLE | WS_CHILD,
            20, startY, 250, 20, m_hMainWindow, (HMENU)ID_STATIC_U004, g_hInstance, nullptr);

        m_hU004Combo = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
            280, startY, 80, 150, m_hMainWindow, (HMENU)ID_COMBO_U004, g_hInstance, nullptr);

        SendMessage(m_hU004Combo, CB_ADDSTRING, 0, (LPARAM)L"200");
        SendMessage(m_hU004Combo, CB_ADDSTRING, 0, (LPARAM)L"400");
        SendMessage(m_hU004Combo, CB_ADDSTRING, 0, (LPARAM)L"600");
        SendMessage(m_hU004Combo, CB_ADDSTRING, 0, (LPARAM)L"800");
        SendMessage(m_hU004Combo, CB_ADDSTRING, 0, (LPARAM)L"1000");
        SendMessage(m_hU004Combo, CB_SETCURSEL, 2, 0); // Default to 600

        CreateWindow(L"BUTTON", L"Send U004", WS_VISIBLE | WS_CHILD,
            370, startY, 80, 25, m_hMainWindow, (HMENU)ID_BUTTON_U004, g_hInstance, nullptr);
        startY += 35;

        // U005: GPIO Input
        CreateWindow(L"STATIC", L"U005 - Enable/disable GPIO input functions:", WS_VISIBLE | WS_CHILD,
            20, startY, 200, 20, m_hMainWindow, (HMENU)ID_STATIC_U005, g_hInstance, nullptr);

        CreateWindow(L"STATIC", L"Channel:", WS_VISIBLE | WS_CHILD,
            230, startY, 50, 20, m_hMainWindow, nullptr, g_hInstance, nullptr);

        m_hU005ChannelCombo = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
            285, startY, 40, 100, m_hMainWindow, (HMENU)ID_COMBO_U005_CH, g_hInstance, nullptr);
        SendMessage(m_hU005ChannelCombo, CB_ADDSTRING, 0, (LPARAM)L"0");
        SendMessage(m_hU005ChannelCombo, CB_ADDSTRING, 0, (LPARAM)L"1");
        SendMessage(m_hU005ChannelCombo, CB_SETCURSEL, 0, 0);

        CreateWindow(L"STATIC", L"Enable:", WS_VISIBLE | WS_CHILD,
            335, startY, 40, 20, m_hMainWindow, nullptr, g_hInstance, nullptr);

        m_hU005EnableCombo = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
            380, startY, 70, 100, m_hMainWindow, (HMENU)ID_COMBO_U005_EN, g_hInstance, nullptr);
        SendMessage(m_hU005EnableCombo, CB_ADDSTRING, 0, (LPARAM)L"Disable");
        SendMessage(m_hU005EnableCombo, CB_ADDSTRING, 0, (LPARAM)L"Enable");
        SendMessage(m_hU005EnableCombo, CB_SETCURSEL, 1, 0);

        CreateWindow(L"BUTTON", L"Send U005", WS_VISIBLE | WS_CHILD,
            460, startY, 80, 25, m_hMainWindow, (HMENU)ID_BUTTON_U005, g_hInstance, nullptr);
        startY += 35;

        // U006: GPIO Output
        CreateWindow(L"STATIC", L"U006 - Enable/disable GPIO output functions:", WS_VISIBLE | WS_CHILD,
            20, startY, 200, 20, m_hMainWindow, (HMENU)ID_STATIC_U006, g_hInstance, nullptr);

        CreateWindow(L"STATIC", L"Channel:", WS_VISIBLE | WS_CHILD,
            230, startY, 50, 20, m_hMainWindow, nullptr, g_hInstance, nullptr);

        m_hU006ChannelCombo = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
            285, startY, 40, 100, m_hMainWindow, (HMENU)ID_COMBO_U006_CH, g_hInstance, nullptr);
        SendMessage(m_hU006ChannelCombo, CB_ADDSTRING, 0, (LPARAM)L"0");
        SendMessage(m_hU006ChannelCombo, CB_ADDSTRING, 0, (LPARAM)L"1");
        SendMessage(m_hU006ChannelCombo, CB_SETCURSEL, 1, 0);

        CreateWindow(L"STATIC", L"Enable:", WS_VISIBLE | WS_CHILD,
            335, startY, 40, 20, m_hMainWindow, nullptr, g_hInstance, nullptr);

        m_hU006EnableCombo = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
            380, startY, 70, 100, m_hMainWindow, (HMENU)ID_COMBO_U006_EN, g_hInstance, nullptr);
        SendMessage(m_hU006EnableCombo, CB_ADDSTRING, 0, (LPARAM)L"Disable");
        SendMessage(m_hU006EnableCombo, CB_ADDSTRING, 0, (LPARAM)L"Enable");
        SendMessage(m_hU006EnableCombo, CB_SETCURSEL, 1, 0);

        CreateWindow(L"BUTTON", L"Send U006", WS_VISIBLE | WS_CHILD,
            460, startY, 80, 25, m_hMainWindow, (HMENU)ID_BUTTON_U006, g_hInstance, nullptr);
    }

    void CreateDataSection(int startY) {
        // Test All button
        CreateWindow(L"BUTTON", L"Test All Commands", WS_VISIBLE | WS_CHILD,
            20, startY, 150, 35, m_hMainWindow, (HMENU)ID_BUTTON_TEST_ALL, g_hInstance, nullptr);
        startY += 50;

        // Send data section
        CreateWindow(L"STATIC", L"Send Data (Hex):", WS_VISIBLE | WS_CHILD,
            20, startY, 120, 20, m_hMainWindow, nullptr, g_hInstance, nullptr);
        startY += 25;

        m_hSendEdit = CreateWindow(L"EDIT", L"", WS_VISIBLE | WS_CHILD | WS_BORDER,
            20, startY, 800, 25, m_hMainWindow, (HMENU)ID_EDIT_SEND, g_hInstance, nullptr);

        CreateWindow(L"BUTTON", L"Send", WS_VISIBLE | WS_CHILD,
            830, startY, 60, 25, m_hMainWindow, (HMENU)ID_BUTTON_SEND, g_hInstance, nullptr);
        startY += 40;

        // Receive data section
        CreateWindow(L"STATIC", L"Received Data & Results:", WS_VISIBLE | WS_CHILD,
            20, startY, 200, 20, m_hMainWindow, nullptr, g_hInstance, nullptr);

        CreateWindow(L"BUTTON", L"Clear", WS_VISIBLE | WS_CHILD,
            830, startY, 60, 25, m_hMainWindow, (HMENU)ID_BUTTON_CLEAR, g_hInstance, nullptr);
        startY += 25;

        m_hReceiveEdit = CreateWindow(L"EDIT", nullptr,
            WS_VISIBLE | WS_CHILD | WS_BORDER | WS_VSCROLL | ES_MULTILINE | ES_READONLY,
            20, startY, 870, 200, m_hMainWindow, (HMENU)ID_EDIT_RECEIVE, g_hInstance, nullptr);
    }

    void RefreshPortList() {
        if (!m_hPortCombo) return;

        // Clear existing items
        SendMessage(m_hPortCombo, CB_RESETCONTENT, 0, 0);
        m_availablePorts.clear();

        // Find available COM ports
        m_availablePorts = FindAllCOMPorts();

        // Add ports to dropdown
        for (const auto& port : m_availablePorts) {
            SendMessage(m_hPortCombo, CB_ADDSTRING, 0, (LPARAM)port.c_str());
        }

        if (!m_availablePorts.empty()) {
            SendMessage(m_hPortCombo, CB_SETCURSEL, 0, 0);
        }

        // Update status
        if (m_availablePorts.empty()) {
            UpdateStatus(L"No COM ports found");
        } else {
            UpdateStatus(L"Found " + std::to_wstring(m_availablePorts.size()) + L" COM port(s)");
        }
    }

    std::vector<std::wstring> FindAllCOMPorts() {
        std::vector<std::wstring> ports;

        // Registry enumeration for all COM ports
        HKEY hKey;
        if (RegOpenKeyEx(HKEY_LOCAL_MACHINE, L"HARDWARE\\DEVICEMAP\\SERIALCOMM", 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
            DWORD index = 0;
            wchar_t valueName[256];
            wchar_t portName[256];
            DWORD valueNameSize, portNameSize;

            while (true) {
                valueNameSize = sizeof(valueName) / sizeof(wchar_t);
                portNameSize = sizeof(portName);

                LONG result = RegEnumValue(hKey, index++, valueName, &valueNameSize,
                                         nullptr, nullptr, (LPBYTE)portName, &portNameSize);

                if (result == ERROR_NO_MORE_ITEMS) break;
                if (result == ERROR_SUCCESS) {
                    ports.push_back(std::wstring(portName));
                }
            }
            RegCloseKey(hKey);
        }

        return ports;
    }

    bool ConnectToSelectedPort() {
        if (!m_hPortCombo) return false;

        int sel = SendMessage(m_hPortCombo, CB_GETCURSEL, 0, 0);
        if (sel == CB_ERR || sel >= (int)m_availablePorts.size()) {
            LogMessage(L"❌ No port selected");
            return false;
        }

        std::wstring selectedPort = m_availablePorts[sel];
        LogMessage(L"Attempting to connect to " + selectedPort + L"...");

        if (ConnectToPort(selectedPort)) {
            LogMessage(L"✓ Successfully connected to " + selectedPort);
            UpdateStatus(L"Connected to " + selectedPort + L" - Ready for testing");
            return true;
        } else {
            LogMessage(L"❌ Failed to connect to " + selectedPort);
            UpdateStatus(L"Connection failed - Check device and drivers");
            return false;
        }
    }

    bool ConnectToPort(const std::wstring& portName) {
        if (m_hComPort != INVALID_HANDLE_VALUE) {
            CloseHandle(m_hComPort);
            m_hComPort = INVALID_HANDLE_VALUE;
        }

        std::wstring fullPortName = L"\\\\.\\" + portName;

        m_hComPort = CreateFile(
            fullPortName.c_str(),
            GENERIC_READ | GENERIC_WRITE,
            0,
            nullptr,
            OPEN_EXISTING,
            FILE_ATTRIBUTE_NORMAL,
            nullptr
        );

        if (m_hComPort == INVALID_HANDLE_VALUE) {
            DWORD error = GetLastError();
            LogMessage(L"CreateFile failed with error: " + std::to_wstring(error));
            return false;
        }

        // Configure port settings for RS485
        DCB dcb = {0};
        dcb.DCBlength = sizeof(DCB);

        if (!GetCommState(m_hComPort, &dcb)) {
            LogMessage(L"GetCommState failed");
            CloseHandle(m_hComPort);
            m_hComPort = INVALID_HANDLE_VALUE;
            return false;
        }

        dcb.BaudRate = CBR_115200;  // Default baud rate
        dcb.ByteSize = 8;
        dcb.Parity = NOPARITY;
        dcb.StopBits = ONESTOPBIT;
        dcb.fBinary = TRUE;
        dcb.fParity = FALSE;
        dcb.fOutxCtsFlow = FALSE;
        dcb.fOutxDsrFlow = FALSE;
        dcb.fDtrControl = DTR_CONTROL_DISABLE;
        dcb.fDsrSensitivity = FALSE;
        dcb.fTXContinueOnXoff = FALSE;
        dcb.fOutX = FALSE;
        dcb.fInX = FALSE;
        dcb.fErrorChar = FALSE;
        dcb.fNull = FALSE;
        dcb.fRtsControl = RTS_CONTROL_DISABLE;
        dcb.fAbortOnError = FALSE;

        if (!SetCommState(m_hComPort, &dcb)) {
            LogMessage(L"SetCommState failed");
            CloseHandle(m_hComPort);
            m_hComPort = INVALID_HANDLE_VALUE;
            return false;
        }

        // Set timeouts
        COMMTIMEOUTS timeouts = {0};
        timeouts.ReadIntervalTimeout = 50;
        timeouts.ReadTotalTimeoutConstant = 100;
        timeouts.ReadTotalTimeoutMultiplier = 10;
        timeouts.WriteTotalTimeoutConstant = 100;
        timeouts.WriteTotalTimeoutMultiplier = 10;

        if (!SetCommTimeouts(m_hComPort, &timeouts)) {
            LogMessage(L"SetCommTimeouts failed");
            CloseHandle(m_hComPort);
            m_hComPort = INVALID_HANDLE_VALUE;
            return false;
        }

        m_bConnected = true;
        m_currentPort = portName;
        return true;
    }

    void DisconnectFromPort() {
        if (m_hComPort != INVALID_HANDLE_VALUE) {
            CloseHandle(m_hComPort);
            m_hComPort = INVALID_HANDLE_VALUE;
        }
        m_bConnected = false;
        m_currentPort.clear();
        UpdateStatus(L"Disconnected");
        LogMessage(L"Disconnected from RS485 port");
    }

    void UpdateStatus(const std::wstring& status) {
        if (m_hStatusText) {
            SetWindowText(m_hStatusText, status.c_str());
        }
    }

    void LogMessage(const std::wstring& message) {
        if (!m_hReceiveEdit) return;

        // Get current text length
        int textLength = GetWindowTextLength(m_hReceiveEdit);

        // Move cursor to end
        SendMessage(m_hReceiveEdit, EM_SETSEL, textLength, textLength);

        // Add timestamp
        SYSTEMTIME st;
        GetLocalTime(&st);
        wchar_t timestamp[64];
        swprintf_s(timestamp, L"[%02d:%02d:%02d] ", st.wHour, st.wMinute, st.wSecond);

        std::wstring fullText = timestamp + message + L"\r\n";

        // Append text
        SendMessage(m_hReceiveEdit, EM_REPLACESEL, FALSE, (LPARAM)fullText.c_str());

        // Scroll to bottom
        SendMessage(m_hReceiveEdit, EM_SCROLLCARET, 0, 0);
    }

    void SendCommand(const std::string& command, UINT32 value) {
        if (!m_bConnected) {
            LogMessage(L"❌ Not connected to RS485 port");
            return;
        }

        LogMessage(L"Sending " + std::wstring(command.begin(), command.end()) +
                          L" command with value: " + std::to_wstring(value));

        // Create RS485 frame according to protocol
        BYTE frame[16] = {0};
        frame[0] = 0xAA;  // Header
        frame[1] = 0xE1;  // ID byte: function code 0b111 (assign) + slave address 1

        // Command key (4 bytes) - store in payload bytes 2-5
        size_t cmdLen = command.length();
        if (cmdLen > 4) cmdLen = 4;
        memcpy(&frame[2], command.c_str(), cmdLen);

        // Value (4 bytes, little-endian) - store in payload bytes 6-9
        frame[6] = (BYTE)(value & 0xFF);
        frame[7] = (BYTE)((value >> 8) & 0xFF);
        frame[8] = (BYTE)((value >> 16) & 0xFF);
        frame[9] = (BYTE)((value >> 24) & 0xFF);

        // Padding (4 bytes) - payload bytes 10-13
        frame[10] = frame[11] = frame[12] = frame[13] = 0x00;

        // CRC8 (placeholder for now)
        frame[14] = 0x00;

        // Trailer
        frame[15] = 0x0D;

        // Display hex data in send box
        std::wstringstream hexStream;
        for (int i = 0; i < 16; i++) {
            hexStream << std::hex << std::uppercase << std::setfill(L'0') << std::setw(2) << frame[i];
            if (i < 15) hexStream << L" ";
        }
        SetWindowText(m_hSendEdit, hexStream.str().c_str());

        // Send data with error handling
        DWORD bytesWritten;
        if (WriteFile(m_hComPort, frame, 16, &bytesWritten, nullptr)) {
            LogMessage(L"✓ Command sent successfully (" + std::to_wstring(bytesWritten) + L" bytes)");

            // Try to read response with timeout
            BYTE response[16] = {0};
            DWORD bytesRead;

            // Wait a bit for response
            std::this_thread::sleep_for(std::chrono::milliseconds(50));

            if (ReadFile(m_hComPort, response, 16, &bytesRead, nullptr) && bytesRead > 0) {
                std::wstringstream responseStream;
                responseStream << L"Response (" << bytesRead << L" bytes): ";
                for (DWORD i = 0; i < bytesRead; i++) {
                    responseStream << std::hex << std::uppercase << std::setfill(L'0') << std::setw(2) << response[i];
                    if (i < bytesRead - 1) responseStream << L" ";
                }
                LogMessage(responseStream.str());
            } else {
                LogMessage(L"No response received (normal for testing without FPGA)");
            }
        } else {
            DWORD error = GetLastError();
            LogMessage(L"❌ Failed to send command (Error: " + std::to_wstring(error) + L")");
        }
    }

    void SendGPIOCommand(const std::string& command, UINT32 channel, UINT32 enable) {
        if (!m_bConnected) {
            LogMessage(L"❌ Not connected to RS485 port");
            return;
        }

        LogMessage(L"Sending " + std::wstring(command.begin(), command.end()) +
                          L" command - Channel: " + std::to_wstring(channel) +
                          L", Enable: " + std::to_wstring(enable));

        // Create RS485 frame with dual integer format for GPIO
        BYTE frame[16] = {0};
        frame[0] = 0xAA;  // Header
        frame[1] = 0xE1;  // ID byte: function code 0b111 (assign) + slave address 1

        // Command key (4 bytes)
        size_t cmdLen = command.length();
        if (cmdLen > 4) cmdLen = 4;
        memcpy(&frame[2], command.c_str(), cmdLen);

        // Channel (lower 32 bits) - bytes 6-9
        frame[6] = (BYTE)(channel & 0xFF);
        frame[7] = (BYTE)((channel >> 8) & 0xFF);
        frame[8] = (BYTE)((channel >> 16) & 0xFF);
        frame[9] = (BYTE)((channel >> 24) & 0xFF);

        // Enable flag (upper 32 bits) - bytes 10-13
        frame[10] = (BYTE)(enable & 0xFF);
        frame[11] = (BYTE)((enable >> 8) & 0xFF);
        frame[12] = (BYTE)((enable >> 16) & 0xFF);
        frame[13] = (BYTE)((enable >> 24) & 0xFF);

        // CRC8 (placeholder)
        frame[14] = 0x00;

        // Trailer
        frame[15] = 0x0D;

        // Display and send
        std::wstringstream hexStream;
        for (int i = 0; i < 16; i++) {
            hexStream << std::hex << std::uppercase << std::setfill(L'0') << std::setw(2) << frame[i];
            if (i < 15) hexStream << L" ";
        }
        SetWindowText(m_hSendEdit, hexStream.str().c_str());

        DWORD bytesWritten;
        if (WriteFile(m_hComPort, frame, 16, &bytesWritten, nullptr)) {
            LogMessage(L"✓ GPIO command sent successfully (" + std::to_wstring(bytesWritten) + L" bytes)");

            // Try to read response
            BYTE response[16] = {0};
            DWORD bytesRead;
            std::this_thread::sleep_for(std::chrono::milliseconds(50));

            if (ReadFile(m_hComPort, response, 16, &bytesRead, nullptr) && bytesRead > 0) {
                std::wstringstream responseStream;
                responseStream << L"Response: ";
                for (DWORD i = 0; i < bytesRead; i++) {
                    responseStream << std::hex << std::uppercase << std::setfill(L'0') << std::setw(2) << response[i];
                    if (i < bytesRead - 1) responseStream << L" ";
                }
                LogMessage(responseStream.str());
            } else {
                LogMessage(L"No response received (normal for testing without FPGA)");
            }
        } else {
            DWORD error = GetLastError();
            LogMessage(L"❌ Failed to send GPIO command (Error: " + std::to_wstring(error) + L")");
        }
    }

    void TestAllCommands() {
        if (!m_bConnected) {
            LogMessage(L"❌ Not connected - please connect first");
            return;
        }

        LogMessage(L"=== Testing All Commands ===");

        // Test S-series commands
        LogMessage(L"Testing S-series (System Configuration) commands...");
        SendCommand("S001", 5);
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        SendCommand("S002", 115200);
        std::this_thread::sleep_for(std::chrono::milliseconds(200));

        // Test U-series commands
        LogMessage(L"Testing U-series (User Configuration) commands...");
        SendCommand("U001", 250);
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        SendCommand("U002", 1500);
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        SendCommand("U003", 3);
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        SendCommand("U004", 600);
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        SendGPIOCommand("U005", 0, 1);
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        SendGPIOCommand("U006", 1, 1);

        LogMessage(L"=== All Commands Test Completed ===");
    }

    void SendRawData() {
        if (!m_bConnected) {
            LogMessage(L"❌ Not connected to RS485 port");
            return;
        }

        wchar_t buffer[1024];
        GetWindowText(m_hSendEdit, buffer, 1024);

        std::wstring hexData(buffer);
        if (hexData.empty()) {
            LogMessage(L"❌ No data to send");
            return;
        }

        LogMessage(L"Sending raw data: " + hexData);

        // Parse hex string and send
        std::vector<BYTE> data;
        std::wstringstream ss(hexData);
        std::wstring hexByte;

        while (ss >> hexByte) {
            if (hexByte.length() == 2) {
                try {
                    BYTE byte = (BYTE)std::stoul(hexByte, nullptr, 16);
                    data.push_back(byte);
                } catch (...) {
                    LogMessage(L"❌ Invalid hex data format");
                    return;
                }
            }
        }

        if (!data.empty()) {
            DWORD bytesWritten;
            if (WriteFile(m_hComPort, data.data(), (DWORD)data.size(), &bytesWritten, nullptr)) {
                LogMessage(L"✓ Raw data sent successfully (" + std::to_wstring(bytesWritten) + L" bytes)");
            } else {
                LogMessage(L"❌ Failed to send raw data");
            }
        }
    }

    void HandleCommand(WORD commandId) {
        try {
            switch (commandId) {
            case ID_BUTTON_REFRESH_PORTS:
                RefreshPortList();
                break;

            case ID_BUTTON_CONNECT:
                if (!ConnectToSelectedPort()) {
                    LogMessage(L"Connection failed. Please check:");
                    LogMessage(L"1. RS485 device is connected");
                    LogMessage(L"2. Correct COM port is selected");
                    LogMessage(L"3. No other application is using the port");
                }
                break;

            case ID_BUTTON_DISCONNECT:
                DisconnectFromPort();
                break;

            case ID_BUTTON_S001: {
                wchar_t buffer[32];
                GetWindowText(m_hS001Edit, buffer, 32);
                int value = _wtoi(buffer);
                if (value >= 1 && value <= 31) {
                    SendCommand("S001", value);
                } else {
                    LogMessage(L"❌ S001 value must be between 1-31");
                }
                break;
            }

            case ID_BUTTON_S002: {
                int sel = SendMessage(m_hS002Combo, CB_GETCURSEL, 0, 0);
                if (sel != CB_ERR) {
                    UINT32 baudRates[] = {9600, 19200, 38400, 57600, 115200};
                    SendCommand("S002", baudRates[sel]);
                } else {
                    LogMessage(L"❌ Please select a baud rate");
                }
                break;
            }

            case ID_BUTTON_U001: {
                wchar_t buffer[32];
                GetWindowText(m_hU001Edit, buffer, 32);
                int value = _wtoi(buffer);
                if (value >= 40 && value <= 500) {
                    SendCommand("U001", value);
                } else {
                    LogMessage(L"❌ U001 value must be between 40-500 mA");
                }
                break;
            }

            case ID_BUTTON_U002: {
                wchar_t buffer[32];
                GetWindowText(m_hU002Edit, buffer, 32);
                int value = _wtoi(buffer);
                if (value >= 1000 && value <= 2000) {
                    SendCommand("U002", value);
                } else {
                    LogMessage(L"❌ U002 value must be between 1000-2000 mA");
                }
                break;
            }

            case ID_BUTTON_U003: {
                int sel = SendMessage(m_hU003Combo, CB_GETCURSEL, 0, 0);
                if (sel != CB_ERR) {
                    SendCommand("U003", sel + 1); // 1-5
                } else {
                    LogMessage(L"❌ Please select detection count");
                }
                break;
            }

            case ID_BUTTON_U004: {
                int sel = SendMessage(m_hU004Combo, CB_GETCURSEL, 0, 0);
                if (sel != CB_ERR) {
                    UINT32 durations[] = {200, 400, 600, 800, 1000};
                    SendCommand("U004", durations[sel]);
                } else {
                    LogMessage(L"❌ Please select power cycle duration");
                }
                break;
            }

            case ID_BUTTON_U005: {
                int channel = SendMessage(m_hU005ChannelCombo, CB_GETCURSEL, 0, 0);
                int enable = SendMessage(m_hU005EnableCombo, CB_GETCURSEL, 0, 0);
                if (channel != CB_ERR && enable != CB_ERR) {
                    SendGPIOCommand("U005", channel, enable);
                } else {
                    LogMessage(L"❌ Please select channel and enable/disable for U005");
                }
                break;
            }

            case ID_BUTTON_U006: {
                int channel = SendMessage(m_hU006ChannelCombo, CB_GETCURSEL, 0, 0);
                int enable = SendMessage(m_hU006EnableCombo, CB_GETCURSEL, 0, 0);
                if (channel != CB_ERR && enable != CB_ERR) {
                    SendGPIOCommand("U006", channel, enable);
                } else {
                    LogMessage(L"❌ Please select channel and enable/disable for U006");
                }
                break;
            }

            case ID_BUTTON_TEST_ALL:
                TestAllCommands();
                break;

            case ID_BUTTON_CLEAR:
                SetWindowText(m_hReceiveEdit, L"");
                LogMessage(L"Display cleared - Ready for new tests");
                break;

            case ID_BUTTON_SEND:
                SendRawData();
                break;

            default:
                break;
            }
        } catch (const std::exception& e) {
            std::string errorMsg = "Exception in HandleCommand: " + std::string(e.what());
            LogMessage(std::wstring(errorMsg.begin(), errorMsg.end()));
        } catch (...) {
            LogMessage(L"Unknown exception in HandleCommand");
        }
    }

    int RunMessageLoop() {
        MSG msg;
        while (GetMessage(&msg, nullptr, 0, 0)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
        }
        return (int)msg.wParam;
    }
};

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    try {
        RS485TestApplication app;

        if (!app.Initialize(hInstance)) {
            MessageBox(nullptr, L"Failed to initialize RS485 Test Application", L"Error", MB_OK | MB_ICONERROR);
            return -1;
        }

        return app.RunMessageLoop();
    } catch (const std::exception& e) {
        std::string errorMsg = "Application exception: " + std::string(e.what());
        MessageBoxA(nullptr, errorMsg.c_str(), "Fatal Error", MB_OK | MB_ICONERROR);
        return -1;
    } catch (...) {
        MessageBox(nullptr, L"Unknown application exception", L"Fatal Error", MB_OK | MB_ICONERROR);
        return -1;
    }
}
