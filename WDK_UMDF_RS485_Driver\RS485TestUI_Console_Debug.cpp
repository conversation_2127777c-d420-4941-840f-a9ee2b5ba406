#include <windows.h>
#include <iostream>
#include <io.h>
#include <fcntl.h>

void CreateConsole() {
    AllocConsole();
    freopen_s((FILE**)stdout, "CONOUT$", "w", stdout);
    freopen_s((FILE**)stderr, "CONOUT$", "w", stderr);
    freopen_s((FILE**)stdin, "CONIN$", "r", stdin);
    std::wcout.clear();
    std::cout.clear();
    std::wcerr.clear();
    std::cerr.clear();
    std::wcin.clear();
    std::cin.clear();
}

LRESULT CALLBACK WindowProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    std::wcout << L"WindowProc called with message: " << uMsg << std::endl;
    
    switch (uMsg) {
    case WM_CREATE: {
        std::wcout << L"WM_CREATE received" << std::endl;

        // Create a simple button to test
        HWND hButton = CreateWindow(L"BUTTON", L"Test Button",
            WS_VISIBLE | WS_CHILD,
            50, 50, 100, 30,
            hWnd, (HMENU)1001, GetModuleHandle(nullptr), nullptr);

        if (hButton) {
            std::wcout << L"Button created successfully" << std::endl;
        } else {
            std::wcout << L"Failed to create button, error: " << GetLastError() << std::endl;
        }

        // Create a static text
        HWND hStatic = CreateWindow(L"STATIC", L"Hello World - RS485 Test",
            WS_VISIBLE | WS_CHILD,
            50, 20, 200, 20,
            hWnd, nullptr, GetModuleHandle(nullptr), nullptr);

        if (hStatic) {
            std::wcout << L"Static text created successfully" << std::endl;
        } else {
            std::wcout << L"Failed to create static text, error: " << GetLastError() << std::endl;
        }

        return 0;
    }

    case WM_COMMAND:
        std::wcout << L"WM_COMMAND received" << std::endl;
        if (LOWORD(wParam) == 1001) {
            std::wcout << L"Button clicked!" << std::endl;
            MessageBox(hWnd, L"Button clicked!", L"Test", MB_OK);
        }
        return 0;

    case WM_DESTROY:
        std::wcout << L"WM_DESTROY received" << std::endl;
        PostQuitMessage(0);
        return 0;

    case WM_PAINT: {
        std::wcout << L"WM_PAINT received" << std::endl;
        PAINTSTRUCT ps;
        HDC hdc = BeginPaint(hWnd, &ps);
        
        // Draw something to verify painting works
        TextOut(hdc, 10, 100, L"Paint message received", 22);
        
        EndPaint(hWnd, &ps);
        return 0;
    }
    }

    return DefWindowProc(hWnd, uMsg, wParam, lParam);
}

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    CreateConsole();
    
    std::wcout << L"Starting RS485 debug application" << std::endl;
    std::wcout << L"hInstance: " << hInstance << std::endl;
    std::wcout << L"nCmdShow: " << nCmdShow << std::endl;
    
    // Register window class
    WNDCLASS wc = {};
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = hInstance;
    wc.lpszClassName = L"DebugRS485Test";
    wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    wc.hCursor = LoadCursor(nullptr, IDC_ARROW);

    if (!RegisterClass(&wc)) {
        std::wcout << L"Failed to register window class, error: " << GetLastError() << std::endl;
        system("pause");
        return -1;
    }
    
    std::wcout << L"Window class registered successfully" << std::endl;

    // Create window
    HWND hWnd = CreateWindowEx(
        0,
        L"DebugRS485Test",
        L"Debug RS485 Test",
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT,
        400, 300,
        nullptr, nullptr, hInstance, nullptr
    );

    if (!hWnd) {
        std::wcout << L"Failed to create window, error: " << GetLastError() << std::endl;
        system("pause");
        return -1;
    }
    
    std::wcout << L"Window created successfully, handle: " << hWnd << std::endl;

    ShowWindow(hWnd, nCmdShow);
    UpdateWindow(hWnd);
    
    std::wcout << L"Window shown and updated" << std::endl;
    std::wcout << L"Starting message loop..." << std::endl;

    // Message loop
    MSG msg;
    int messageCount = 0;
    while (GetMessage(&msg, nullptr, 0, 0)) {
        messageCount++;
        if (messageCount % 100 == 0) {
            std::wcout << L"Processed " << messageCount << L" messages" << std::endl;
        }
        
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }
    
    std::wcout << L"Message loop ended, total messages: " << messageCount << std::endl;
    std::wcout << L"Exit code: " << msg.wParam << std::endl;
    
    system("pause");
    return (int)msg.wParam;
}
