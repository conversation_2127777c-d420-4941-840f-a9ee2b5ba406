#include <windows.h>
#include <commctrl.h>
#include <setupapi.h>
#include <devguid.h>
#include <regstr.h>
#include <string>
#include <vector>
#include <sstream>
#include <iomanip>

#pragma comment(lib, "user32.lib")
#pragma comment(lib, "gdi32.lib")
#pragma comment(lib, "comctl32.lib")
#pragma comment(lib, "setupapi.lib")

// Window dimensions
#define WINDOW_WIDTH 1000
#define WINDOW_HEIGHT 800

// Control IDs
#define ID_BUTTON_AUTO_CONNECT  1010
#define ID_BUTTON_DISCONNECT    1011
#define ID_STATIC_STATUS        1012
#define ID_PROGRESS_BAR         1013

// S-Series Commands
#define ID_STATIC_S001          1020
#define ID_EDIT_S001            1021
#define ID_BUTTON_S001          1022
#define ID_STATIC_S002          1023
#define ID_COMBO_S002           1024
#define ID_BUTTON_S002          1025

// U-Series Commands
#define ID_STATIC_U001          1030
#define ID_EDIT_U001            1031
#define ID_BUTTON_U001          1032
#define ID_STATIC_U002          1033
#define ID_EDIT_U002            1034
#define ID_BUTTON_U002          1035
#define ID_STATIC_U003          1036
#define ID_COMBO_U003           1037
#define ID_BUTTON_U003          1038
#define ID_STATIC_U004          1039
#define ID_COMBO_U004           1040
#define ID_BUTTON_U004          1041
#define ID_STATIC_U005          1042
#define ID_COMBO_U005_CH        1043
#define ID_COMBO_U005_EN        1044
#define ID_BUTTON_U005          1045
#define ID_STATIC_U006          1046
#define ID_COMBO_U006_CH        1047
#define ID_COMBO_U006_EN        1048
#define ID_BUTTON_U006          1049

// Data display
#define ID_EDIT_SEND            1050
#define ID_BUTTON_SEND          1051
#define ID_EDIT_RECEIVE         1052
#define ID_BUTTON_CLEAR         1053
#define ID_BUTTON_TEST_ALL      1054

class RS485TestApp {
private:
    HWND m_hWnd;
    HWND m_hStaticStatus;
    HWND m_hProgressBar;
    HWND m_hEditSend;
    HWND m_hEditReceive;
    
    // S-Series controls
    HWND m_hEditS001;      // Slave address input (1-31)

    
    // U-Series controls
    HWND m_hEditU001;      // SEL threshold input (40-500)
    HWND m_hEditU002;      // Max amplitude input (1000-2000)
    HWND m_hComboU003;     // Detection count dropdown (1-5)
    HWND m_hComboU004;     // Power cycle duration dropdown
    HWND m_hComboU005Ch;   // GPIO input channel (0-1)
    HWND m_hComboU005En;   // GPIO input enable (0-1)
    HWND m_hComboU006Ch;   // GPIO output channel (0-1)
    HWND m_hComboU006En;   // GPIO output enable (0-1)
    
    HANDLE m_hComPort;
    bool m_bConnected;
    std::wstring m_currentPort;
    std::vector<std::wstring> m_availablePorts;

public:
    RS485TestApp() : m_hWnd(nullptr), m_hComPort(INVALID_HANDLE_VALUE), m_bConnected(false) {}
    
    ~RS485TestApp() {
        if (m_hComPort != INVALID_HANDLE_VALUE) {
            CloseHandle(m_hComPort);
        }
    }

    bool Initialize(HINSTANCE hInstance) {
        // Register window class
        WNDCLASSEX wc = {0};
        wc.cbSize = sizeof(WNDCLASSEX);
        wc.style = CS_HREDRAW | CS_VREDRAW;
        wc.lpfnWndProc = WindowProc;
        wc.hInstance = hInstance;
        wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
        wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
        wc.lpszClassName = L"RS485TestUI";
        wc.hIcon = LoadIcon(nullptr, IDI_APPLICATION);
        wc.hIconSm = LoadIcon(nullptr, IDI_APPLICATION);

        if (!RegisterClassEx(&wc)) {
            return false;
        }

        // Create main window
        m_hWnd = CreateWindowEx(
            0,
            L"RS485TestUI",
            L"RS485 Driver Test UI - Enhanced Version",
            WS_OVERLAPPEDWINDOW,
            CW_USEDEFAULT, CW_USEDEFAULT,
            WINDOW_WIDTH, WINDOW_HEIGHT,
            nullptr, nullptr, hInstance, this
        );

        if (!m_hWnd) {
            return false;
        }

        ShowWindow(m_hWnd, SW_SHOW);
        UpdateWindow(m_hWnd);
        return true;
    }

    void CreateControls() {
        // Initialize common controls
        INITCOMMONCONTROLSEX icex;
        icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
        icex.dwICC = ICC_PROGRESS_CLASS;
        InitCommonControlsEx(&icex);

        int yPos = 20;
        
        // Connection section
        CreateWindow(L"STATIC", L"RS485 Connection", WS_VISIBLE | WS_CHILD | SS_LEFT,
            20, yPos, 200, 20, m_hWnd, nullptr, nullptr, nullptr);
        yPos += 25;

        CreateWindow(L"BUTTON", L"Auto Connect", WS_VISIBLE | WS_CHILD,
            20, yPos, 100, 30, m_hWnd, (HMENU)ID_BUTTON_AUTO_CONNECT, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Disconnect", WS_VISIBLE | WS_CHILD,
            130, yPos, 100, 30, m_hWnd, (HMENU)ID_BUTTON_DISCONNECT, nullptr, nullptr);
        yPos += 40;

        // Status display
        CreateWindow(L"STATIC", L"Status:", WS_VISIBLE | WS_CHILD,
            20, yPos, 60, 20, m_hWnd, nullptr, nullptr, nullptr);

        m_hStaticStatus = CreateWindow(L"STATIC", L"Disconnected", WS_VISIBLE | WS_CHILD | SS_SUNKEN,
            90, yPos, 300, 20, m_hWnd, (HMENU)ID_STATIC_STATUS, nullptr, nullptr);

        m_hProgressBar = CreateWindow(PROGRESS_CLASS, nullptr, WS_VISIBLE | WS_CHILD,
            400, yPos, 200, 20, m_hWnd, (HMENU)ID_PROGRESS_BAR, nullptr, nullptr);
        SendMessage(m_hProgressBar, PBM_SETRANGE, 0, MAKELPARAM(0, 100));
        yPos += 40;

        // Create command sections
        CreateSystemCommands(yPos);
        CreateUserCommands(yPos + 120);
        CreateDataSection(yPos + 360);
    }

    void CreateSystemCommands(int startY) {
        // S-Series Commands Section
        CreateWindow(L"STATIC", L"System Configuration Commands (S-Series)", 
            WS_VISIBLE | WS_CHILD | SS_LEFT,
            20, startY, 400, 20, m_hWnd, nullptr, nullptr, nullptr);
        startY += 30;

        // S001: Slave Address (1-31)
        CreateWindow(L"STATIC", L"S001 - Slave Address (1-31):", WS_VISIBLE | WS_CHILD,
            20, startY, 200, 20, m_hWnd, (HMENU)ID_STATIC_S001, nullptr, nullptr);
        
        m_hEditS001 = CreateWindow(L"EDIT", L"5", WS_VISIBLE | WS_CHILD | WS_BORDER,
            230, startY, 60, 25, m_hWnd, (HMENU)ID_EDIT_S001, nullptr, nullptr);
        
        CreateWindow(L"BUTTON", L"Send S001", WS_VISIBLE | WS_CHILD,
            300, startY, 80, 25, m_hWnd, (HMENU)ID_BUTTON_S001, nullptr, nullptr);
        startY += 35;

        // S002: Baud Rate (dropdown)
        CreateWindow(L"STATIC", L"S002 - Baud Rate:", WS_VISIBLE | WS_CHILD,
            20, startY, 200, 20, m_hWnd, (HMENU)ID_STATIC_S002, nullptr, nullptr);
        
        m_hComboS002 = CreateWindow(L"COMBOBOX", nullptr, 
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST | WS_VSCROLL,
            230, startY, 100, 200, m_hWnd, (HMENU)ID_COMBO_S002, nullptr, nullptr);
        
        // Add baud rate options
        SendMessage(m_hComboS002, CB_ADDSTRING, 0, (LPARAM)L"9600");
        SendMessage(m_hComboS002, CB_ADDSTRING, 0, (LPARAM)L"19200");
        SendMessage(m_hComboS002, CB_ADDSTRING, 0, (LPARAM)L"38400");
        SendMessage(m_hComboS002, CB_ADDSTRING, 0, (LPARAM)L"57600");
        SendMessage(m_hComboS002, CB_ADDSTRING, 0, (LPARAM)L"115200");
        SendMessage(m_hComboS002, CB_SETCURSEL, 4, 0); // Default to 115200
        
        CreateWindow(L"BUTTON", L"Send S002", WS_VISIBLE | WS_CHILD,
            340, startY, 80, 25, m_hWnd, (HMENU)ID_BUTTON_S002, nullptr, nullptr);
    }

    void CreateUserCommands(int startY) {
        // U-Series Commands Section
        CreateWindow(L"STATIC", L"User Configuration Commands (U-Series)", 
            WS_VISIBLE | WS_CHILD | SS_LEFT,
            20, startY, 400, 20, m_hWnd, nullptr, nullptr, nullptr);
        startY += 30;

        // U001: SEL Threshold (40-500)
        CreateWindow(L"STATIC", L"U001 - SEL Threshold (40-500 mA):", WS_VISIBLE | WS_CHILD,
            20, startY, 200, 20, m_hWnd, (HMENU)ID_STATIC_U001, nullptr, nullptr);
        
        m_hEditU001 = CreateWindow(L"EDIT", L"250", WS_VISIBLE | WS_CHILD | WS_BORDER,
            230, startY, 60, 25, m_hWnd, (HMENU)ID_EDIT_U001, nullptr, nullptr);
        
        CreateWindow(L"BUTTON", L"Send U001", WS_VISIBLE | WS_CHILD,
            300, startY, 80, 25, m_hWnd, (HMENU)ID_BUTTON_U001, nullptr, nullptr);
        startY += 35;

        // U002: Max Amplitude (1000-2000)
        CreateWindow(L"STATIC", L"U002 - Max Amplitude (1000-2000 mA):", WS_VISIBLE | WS_CHILD,
            20, startY, 200, 20, m_hWnd, (HMENU)ID_STATIC_U002, nullptr, nullptr);
        
        m_hEditU002 = CreateWindow(L"EDIT", L"1500", WS_VISIBLE | WS_CHILD | WS_BORDER,
            230, startY, 60, 25, m_hWnd, (HMENU)ID_EDIT_U002, nullptr, nullptr);
        
        CreateWindow(L"BUTTON", L"Send U002", WS_VISIBLE | WS_CHILD,
            300, startY, 80, 25, m_hWnd, (HMENU)ID_BUTTON_U002, nullptr, nullptr);
        startY += 35;

        // Continue with remaining U-series commands...
        CreateRemainingUserCommands(startY);
    }

    void CreateRemainingUserCommands(int startY) {
        // U003: Detection Count (1-5)
        CreateWindow(L"STATIC", L"U003 - Detection Count (1-5):", WS_VISIBLE | WS_CHILD,
            20, startY, 200, 20, m_hWnd, (HMENU)ID_STATIC_U003, nullptr, nullptr);

        m_hComboU003 = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
            230, startY, 60, 150, m_hWnd, (HMENU)ID_COMBO_U003, nullptr, nullptr);

        for (int i = 1; i <= 5; i++) {
            wchar_t buffer[10];
            swprintf_s(buffer, L"%d", i);
            SendMessage(m_hComboU003, CB_ADDSTRING, 0, (LPARAM)buffer);
        }
        SendMessage(m_hComboU003, CB_SETCURSEL, 2, 0); // Default to 3

        CreateWindow(L"BUTTON", L"Send U003", WS_VISIBLE | WS_CHILD,
            300, startY, 80, 25, m_hWnd, (HMENU)ID_BUTTON_U003, nullptr, nullptr);
        startY += 35;

        // U004: Power Cycle Duration
        CreateWindow(L"STATIC", L"U004 - Power Cycle (ms):", WS_VISIBLE | WS_CHILD,
            20, startY, 200, 20, m_hWnd, (HMENU)ID_STATIC_U004, nullptr, nullptr);

        m_hComboU004 = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
            230, startY, 80, 150, m_hWnd, (HMENU)ID_COMBO_U004, nullptr, nullptr);

        SendMessage(m_hComboU004, CB_ADDSTRING, 0, (LPARAM)L"200");
        SendMessage(m_hComboU004, CB_ADDSTRING, 0, (LPARAM)L"400");
        SendMessage(m_hComboU004, CB_ADDSTRING, 0, (LPARAM)L"600");
        SendMessage(m_hComboU004, CB_ADDSTRING, 0, (LPARAM)L"800");
        SendMessage(m_hComboU004, CB_ADDSTRING, 0, (LPARAM)L"1000");
        SendMessage(m_hComboU004, CB_SETCURSEL, 2, 0); // Default to 600

        CreateWindow(L"BUTTON", L"Send U004", WS_VISIBLE | WS_CHILD,
            320, startY, 80, 25, m_hWnd, (HMENU)ID_BUTTON_U004, nullptr, nullptr);
        startY += 35;

        // U005: GPIO Input
        CreateWindow(L"STATIC", L"U005 - GPIO Input:", WS_VISIBLE | WS_CHILD,
            20, startY, 120, 20, m_hWnd, (HMENU)ID_STATIC_U005, nullptr, nullptr);

        CreateWindow(L"STATIC", L"Ch:", WS_VISIBLE | WS_CHILD,
            150, startY, 25, 20, m_hWnd, nullptr, nullptr, nullptr);

        m_hComboU005Ch = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
            175, startY, 40, 100, m_hWnd, (HMENU)ID_COMBO_U005_CH, nullptr, nullptr);
        SendMessage(m_hComboU005Ch, CB_ADDSTRING, 0, (LPARAM)L"0");
        SendMessage(m_hComboU005Ch, CB_ADDSTRING, 0, (LPARAM)L"1");
        SendMessage(m_hComboU005Ch, CB_SETCURSEL, 0, 0);

        CreateWindow(L"STATIC", L"En:", WS_VISIBLE | WS_CHILD,
            225, startY, 25, 20, m_hWnd, nullptr, nullptr, nullptr);

        m_hComboU005En = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
            250, startY, 60, 100, m_hWnd, (HMENU)ID_COMBO_U005_EN, nullptr, nullptr);
        SendMessage(m_hComboU005En, CB_ADDSTRING, 0, (LPARAM)L"Disable");
        SendMessage(m_hComboU005En, CB_ADDSTRING, 0, (LPARAM)L"Enable");
        SendMessage(m_hComboU005En, CB_SETCURSEL, 1, 0);

        CreateWindow(L"BUTTON", L"Send U005", WS_VISIBLE | WS_CHILD,
            320, startY, 80, 25, m_hWnd, (HMENU)ID_BUTTON_U005, nullptr, nullptr);
        startY += 35;

        // U006: GPIO Output
        CreateWindow(L"STATIC", L"U006 - GPIO Output:", WS_VISIBLE | WS_CHILD,
            20, startY, 120, 20, m_hWnd, (HMENU)ID_STATIC_U006, nullptr, nullptr);

        CreateWindow(L"STATIC", L"Ch:", WS_VISIBLE | WS_CHILD,
            150, startY, 25, 20, m_hWnd, nullptr, nullptr, nullptr);

        m_hComboU006Ch = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
            175, startY, 40, 100, m_hWnd, (HMENU)ID_COMBO_U006_CH, nullptr, nullptr);
        SendMessage(m_hComboU006Ch, CB_ADDSTRING, 0, (LPARAM)L"0");
        SendMessage(m_hComboU006Ch, CB_ADDSTRING, 0, (LPARAM)L"1");
        SendMessage(m_hComboU006Ch, CB_SETCURSEL, 1, 0);

        CreateWindow(L"STATIC", L"En:", WS_VISIBLE | WS_CHILD,
            225, startY, 25, 20, m_hWnd, nullptr, nullptr, nullptr);

        m_hComboU006En = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
            250, startY, 60, 100, m_hWnd, (HMENU)ID_COMBO_U006_EN, nullptr, nullptr);
        SendMessage(m_hComboU006En, CB_ADDSTRING, 0, (LPARAM)L"Disable");
        SendMessage(m_hComboU006En, CB_ADDSTRING, 0, (LPARAM)L"Enable");
        SendMessage(m_hComboU006En, CB_SETCURSEL, 1, 0);

        CreateWindow(L"BUTTON", L"Send U006", WS_VISIBLE | WS_CHILD,
            320, startY, 80, 25, m_hWnd, (HMENU)ID_BUTTON_U006, nullptr, nullptr);
    }

    void CreateDataSection(int startY) {
        // Test All button
        CreateWindow(L"BUTTON", L"Test All Commands", WS_VISIBLE | WS_CHILD,
            20, startY, 150, 35, m_hWnd, (HMENU)ID_BUTTON_TEST_ALL, nullptr, nullptr);
        startY += 50;

        // Send data section
        CreateWindow(L"STATIC", L"Send Data (Hex):", WS_VISIBLE | WS_CHILD,
            20, startY, 120, 20, m_hWnd, nullptr, nullptr, nullptr);
        startY += 25;

        m_hEditSend = CreateWindow(L"EDIT", L"", WS_VISIBLE | WS_CHILD | WS_BORDER,
            20, startY, 700, 25, m_hWnd, (HMENU)ID_EDIT_SEND, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Send", WS_VISIBLE | WS_CHILD,
            730, startY, 60, 25, m_hWnd, (HMENU)ID_BUTTON_SEND, nullptr, nullptr);
        startY += 40;

        // Receive data section
        CreateWindow(L"STATIC", L"Received Data & Results:", WS_VISIBLE | WS_CHILD,
            20, startY, 200, 20, m_hWnd, nullptr, nullptr, nullptr);

        CreateWindow(L"BUTTON", L"Clear", WS_VISIBLE | WS_CHILD,
            730, startY, 60, 25, m_hWnd, (HMENU)ID_BUTTON_CLEAR, nullptr, nullptr);
        startY += 25;

        m_hEditReceive = CreateWindow(L"EDIT", nullptr,
            WS_VISIBLE | WS_CHILD | WS_BORDER | WS_VSCROLL | ES_MULTILINE | ES_READONLY,
            20, startY, 770, 200, m_hWnd, (HMENU)ID_EDIT_RECEIVE, nullptr, nullptr);
    }

    // Auto-detect and connect to RS485 COM port
    bool AutoConnectRS485() {
        AppendToReceiveBox(L"=== Auto-detecting RS485 COM ports ===");

        std::vector<std::wstring> ftdiPorts = FindFTDIPorts();

        if (ftdiPorts.empty()) {
            AppendToReceiveBox(L"❌ No FTDI/RS485 devices found");
            AppendToReceiveBox(L"Please check:");
            AppendToReceiveBox(L"  1. RS485 device is connected");
            AppendToReceiveBox(L"  2. FTDI drivers are installed");
            AppendToReceiveBox(L"  3. Device is recognized by Windows");
            UpdateStatus(L"No RS485 devices found");
            return false;
        }

        AppendToReceiveBox(L"✓ Found FTDI/RS485 devices:");
        for (const auto& port : ftdiPorts) {
            AppendToReceiveBox(L"  - " + port);
        }

        // Try to connect to each port
        for (const auto& port : ftdiPorts) {
            AppendToReceiveBox(L"Trying to connect to " + port + L"...");

            if (ConnectToPort(port)) {
                AppendToReceiveBox(L"✓ Successfully connected to " + port);
                UpdateStatus(L"Connected to " + port + L" - Ready for testing");
                return true;
            } else {
                AppendToReceiveBox(L"✗ Failed to connect to " + port);
            }
        }

        AppendToReceiveBox(L"❌ Could not connect to any RS485 port");
        UpdateStatus(L"Connection failed - Check device and drivers");
        return false;
    }

    std::vector<std::wstring> FindFTDIPorts() {
        std::vector<std::wstring> ports;

        HDEVINFO hDevInfo = SetupDiGetClassDevs(&GUID_DEVCLASS_PORTS, nullptr, nullptr, DIGCF_PRESENT);
        if (hDevInfo == INVALID_HANDLE_VALUE) {
            return ports;
        }

        SP_DEVINFO_DATA devInfoData;
        devInfoData.cbSize = sizeof(SP_DEVINFO_DATA);

        for (DWORD i = 0; SetupDiEnumDeviceInfo(hDevInfo, i, &devInfoData); i++) {
            wchar_t deviceDesc[256] = {0};
            wchar_t friendlyName[256] = {0};

            // Get device description
            SetupDiGetDeviceRegistryProperty(hDevInfo, &devInfoData, SPDRP_DEVICEDESC,
                nullptr, (PBYTE)deviceDesc, sizeof(deviceDesc), nullptr);

            // Get friendly name
            SetupDiGetDeviceRegistryProperty(hDevInfo, &devInfoData, SPDRP_FRIENDLYNAME,
                nullptr, (PBYTE)friendlyName, sizeof(friendlyName), nullptr);

            std::wstring desc(deviceDesc);
            std::wstring friendly(friendlyName);

            // Look for FTDI devices or USB Serial devices
            if (desc.find(L"FTDI") != std::wstring::npos ||
                friendly.find(L"FTDI") != std::wstring::npos ||
                friendly.find(L"USB Serial") != std::wstring::npos ||
                friendly.find(L"COM") != std::wstring::npos) {

                // Extract COM port number from friendly name
                size_t comPos = friendly.find(L"COM");
                if (comPos != std::wstring::npos) {
                    size_t endPos = friendly.find(L")", comPos);
                    if (endPos != std::wstring::npos) {
                        std::wstring portName = friendly.substr(comPos, endPos - comPos);
                        ports.push_back(portName);
                    }
                }
            }
        }

        SetupDiDestroyDeviceInfoList(hDevInfo);
        return ports;
    }

    bool ConnectToPort(const std::wstring& portName) {
        if (m_hComPort != INVALID_HANDLE_VALUE) {
            CloseHandle(m_hComPort);
            m_hComPort = INVALID_HANDLE_VALUE;
        }

        std::wstring fullPortName = L"\\\\.\\" + portName;

        m_hComPort = CreateFile(
            fullPortName.c_str(),
            GENERIC_READ | GENERIC_WRITE,
            0,
            nullptr,
            OPEN_EXISTING,
            FILE_ATTRIBUTE_NORMAL,
            nullptr
        );

        if (m_hComPort == INVALID_HANDLE_VALUE) {
            return false;
        }

        // Configure port settings for RS485
        DCB dcb = {0};
        dcb.DCBlength = sizeof(DCB);

        if (!GetCommState(m_hComPort, &dcb)) {
            CloseHandle(m_hComPort);
            m_hComPort = INVALID_HANDLE_VALUE;
            return false;
        }

        dcb.BaudRate = CBR_115200;  // Default baud rate
        dcb.ByteSize = 8;
        dcb.Parity = NOPARITY;
        dcb.StopBits = ONESTOPBIT;
        dcb.fBinary = TRUE;
        dcb.fParity = FALSE;
        dcb.fOutxCtsFlow = FALSE;
        dcb.fOutxDsrFlow = FALSE;
        dcb.fDtrControl = DTR_CONTROL_DISABLE;
        dcb.fDsrSensitivity = FALSE;
        dcb.fTXContinueOnXoff = FALSE;
        dcb.fOutX = FALSE;
        dcb.fInX = FALSE;
        dcb.fErrorChar = FALSE;
        dcb.fNull = FALSE;
        dcb.fRtsControl = RTS_CONTROL_DISABLE;
        dcb.fAbortOnError = FALSE;

        if (!SetCommState(m_hComPort, &dcb)) {
            CloseHandle(m_hComPort);
            m_hComPort = INVALID_HANDLE_VALUE;
            return false;
        }

        // Set timeouts
        COMMTIMEOUTS timeouts = {0};
        timeouts.ReadIntervalTimeout = 50;
        timeouts.ReadTotalTimeoutConstant = 100;
        timeouts.ReadTotalTimeoutMultiplier = 10;
        timeouts.WriteTotalTimeoutConstant = 100;
        timeouts.WriteTotalTimeoutMultiplier = 10;

        if (!SetCommTimeouts(m_hComPort, &timeouts)) {
            CloseHandle(m_hComPort);
            m_hComPort = INVALID_HANDLE_VALUE;
            return false;
        }

        m_bConnected = true;
        m_currentPort = portName;
        return true;
    }

    void Disconnect() {
        if (m_hComPort != INVALID_HANDLE_VALUE) {
            CloseHandle(m_hComPort);
            m_hComPort = INVALID_HANDLE_VALUE;
        }
        m_bConnected = false;
        m_currentPort.clear();
        UpdateStatus(L"Disconnected");
        AppendToReceiveBox(L"Disconnected from RS485 port");
    }

    void UpdateStatus(const std::wstring& status) {
        if (m_hStaticStatus) {
            SetWindowText(m_hStaticStatus, status.c_str());
        }
    }

    void AppendToReceiveBox(const std::wstring& text) {
        if (!m_hEditReceive) return;

        // Get current text length
        int textLength = GetWindowTextLength(m_hEditReceive);

        // Move cursor to end
        SendMessage(m_hEditReceive, EM_SETSEL, textLength, textLength);

        // Add timestamp
        SYSTEMTIME st;
        GetLocalTime(&st);
        wchar_t timestamp[64];
        swprintf_s(timestamp, L"[%02d:%02d:%02d] ", st.wHour, st.wMinute, st.wSecond);

        std::wstring fullText = timestamp + text + L"\r\n";

        // Append text
        SendMessage(m_hEditReceive, EM_REPLACESEL, FALSE, (LPARAM)fullText.c_str());

        // Scroll to bottom
        SendMessage(m_hEditReceive, EM_SCROLLCARET, 0, 0);
    }

    void SendCommand(const std::string& command, UINT32 value) {
        if (!m_bConnected) {
            AppendToReceiveBox(L"❌ Not connected to RS485 port");
            return;
        }

        AppendToReceiveBox(L"Sending " + std::wstring(command.begin(), command.end()) +
                          L" command with value: " + std::to_wstring(value));

        // Create RS485 frame
        BYTE frame[16] = {0};
        frame[0] = 0xAA;  // Header
        frame[1] = 0x01;  // Default slave ID (will be updated by S001)

        // Command key (4 bytes)
        memcpy(&frame[2], command.c_str(), 4);

        // Value (4 bytes, little-endian)
        frame[6] = (BYTE)(value & 0xFF);
        frame[7] = (BYTE)((value >> 8) & 0xFF);
        frame[8] = (BYTE)((value >> 16) & 0xFF);
        frame[9] = (BYTE)((value >> 24) & 0xFF);

        // Padding (4 bytes)
        frame[10] = frame[11] = frame[12] = frame[13] = 0x00;

        // CRC (placeholder)
        frame[14] = 0x00;

        // Trailer
        frame[15] = 0x0D;

        // Display hex data
        std::wstringstream hexStream;
        for (int i = 0; i < 16; i++) {
            hexStream << std::hex << std::uppercase << std::setfill(L'0') << std::setw(2) << frame[i];
            if (i < 15) hexStream << L" ";
        }
        SetWindowText(m_hEditSend, hexStream.str().c_str());

        // Send data
        DWORD bytesWritten;
        if (WriteFile(m_hComPort, frame, 16, &bytesWritten, nullptr)) {
            AppendToReceiveBox(L"✓ Command sent successfully (" + std::to_wstring(bytesWritten) + L" bytes)");

            // Try to read response
            BYTE response[16] = {0};
            DWORD bytesRead;
            if (ReadFile(m_hComPort, response, 16, &bytesRead, nullptr) && bytesRead > 0) {
                std::wstringstream responseStream;
                responseStream << L"Response: ";
                for (DWORD i = 0; i < bytesRead; i++) {
                    responseStream << std::hex << std::uppercase << std::setfill(L'0') << std::setw(2) << response[i];
                    if (i < bytesRead - 1) responseStream << L" ";
                }
                AppendToReceiveBox(responseStream.str());
            } else {
                AppendToReceiveBox(L"No response received (FPGA may not be ready)");
            }
        } else {
            AppendToReceiveBox(L"❌ Failed to send command");
        }
    }

    void SendGPIOCommand(const std::string& command, UINT32 channel, UINT32 enable) {
        if (!m_bConnected) {
            AppendToReceiveBox(L"❌ Not connected to RS485 port");
            return;
        }

        AppendToReceiveBox(L"Sending " + std::wstring(command.begin(), command.end()) +
                          L" command - Channel: " + std::to_wstring(channel) +
                          L", Enable: " + std::to_wstring(enable));

        // Create RS485 frame with dual integer format
        BYTE frame[16] = {0};
        frame[0] = 0xAA;  // Header
        frame[1] = 0x01;  // Default slave ID

        // Command key (4 bytes)
        memcpy(&frame[2], command.c_str(), 4);

        // Channel (lower 32 bits)
        frame[6] = (BYTE)(channel & 0xFF);
        frame[7] = (BYTE)((channel >> 8) & 0xFF);
        frame[8] = (BYTE)((channel >> 16) & 0xFF);
        frame[9] = (BYTE)((channel >> 24) & 0xFF);

        // Enable flag (upper 32 bits)
        frame[10] = (BYTE)(enable & 0xFF);
        frame[11] = (BYTE)((enable >> 8) & 0xFF);
        frame[12] = (BYTE)((enable >> 16) & 0xFF);
        frame[13] = (BYTE)((enable >> 24) & 0xFF);

        // CRC (placeholder)
        frame[14] = 0x00;

        // Trailer
        frame[15] = 0x0D;

        // Display and send
        std::wstringstream hexStream;
        for (int i = 0; i < 16; i++) {
            hexStream << std::hex << std::uppercase << std::setfill(L'0') << std::setw(2) << frame[i];
            if (i < 15) hexStream << L" ";
        }
        SetWindowText(m_hEditSend, hexStream.str().c_str());

        DWORD bytesWritten;
        if (WriteFile(m_hComPort, frame, 16, &bytesWritten, nullptr)) {
            AppendToReceiveBox(L"✓ GPIO command sent successfully");
        } else {
            AppendToReceiveBox(L"❌ Failed to send GPIO command");
        }
    }

    static LRESULT CALLBACK WindowProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
        RS485TestApp* pApp = nullptr;

        if (uMsg == WM_NCCREATE) {
            CREATESTRUCT* pCreate = reinterpret_cast<CREATESTRUCT*>(lParam);
            pApp = reinterpret_cast<RS485TestApp*>(pCreate->lpCreateParams);
            SetWindowLongPtr(hWnd, GWLP_USERDATA, reinterpret_cast<LONG_PTR>(pApp));
        } else {
            pApp = reinterpret_cast<RS485TestApp*>(GetWindowLongPtr(hWnd, GWLP_USERDATA));
        }

        if (pApp) {
            return pApp->HandleMessage(hWnd, uMsg, wParam, lParam);
        }

        return DefWindowProc(hWnd, uMsg, wParam, lParam);
    }

    LRESULT HandleMessage(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
        switch (uMsg) {
        case WM_CREATE:
            CreateControls();
            AppendToReceiveBox(L"RS485 Test UI Enhanced - Ready");
            AppendToReceiveBox(L"Click 'Auto Connect' to detect and connect to RS485 device");
            return 0;

        case WM_COMMAND:
            HandleCommand(LOWORD(wParam));
            return 0;

        case WM_DESTROY:
            Disconnect();
            PostQuitMessage(0);
            return 0;
        }

        return DefWindowProc(hWnd, uMsg, wParam, lParam);
    }

    void HandleCommand(WORD commandId) {
        switch (commandId) {
        case ID_BUTTON_AUTO_CONNECT:
            AutoConnectRS485();
            break;

        case ID_BUTTON_DISCONNECT:
            Disconnect();
            break;

        case ID_BUTTON_S001: {
            wchar_t buffer[32];
            GetWindowText(m_hEditS001, buffer, 32);
            int value = _wtoi(buffer);
            if (value >= 1 && value <= 31) {
                SendCommand("S001", value);
            } else {
                AppendToReceiveBox(L"❌ S001 value must be between 1-31");
            }
            break;
        }



        case ID_BUTTON_U001: {
            wchar_t buffer[32];
            GetWindowText(m_hEditU001, buffer, 32);
            int value = _wtoi(buffer);
            if (value >= 40 && value <= 500) {
                SendCommand("U001", value);
            } else {
                AppendToReceiveBox(L"❌ U001 value must be between 40-500 mA");
            }
            break;
        }

        case ID_BUTTON_U002: {
            wchar_t buffer[32];
            GetWindowText(m_hEditU002, buffer, 32);
            int value = _wtoi(buffer);
            if (value >= 1000 && value <= 2000) {
                SendCommand("U002", value);
            } else {
                AppendToReceiveBox(L"❌ U002 value must be between 1000-2000 mA");
            }
            break;
        }

        case ID_BUTTON_U003: {
            int sel = SendMessage(m_hComboU003, CB_GETCURSEL, 0, 0);
            if (sel != CB_ERR) {
                SendCommand("U003", sel + 1); // 1-5
            }
            break;
        }

        case ID_BUTTON_U004: {
            int sel = SendMessage(m_hComboU004, CB_GETCURSEL, 0, 0);
            if (sel != CB_ERR) {
                UINT32 durations[] = {200, 400, 600, 800, 1000};
                SendCommand("U004", durations[sel]);
            }
            break;
        }

        case ID_BUTTON_U005: {
            int channel = SendMessage(m_hComboU005Ch, CB_GETCURSEL, 0, 0);
            int enable = SendMessage(m_hComboU005En, CB_GETCURSEL, 0, 0);
            if (channel != CB_ERR && enable != CB_ERR) {
                SendGPIOCommand("U005", channel, enable);
            }
            break;
        }

        case ID_BUTTON_U006: {
            int channel = SendMessage(m_hComboU006Ch, CB_GETCURSEL, 0, 0);
            int enable = SendMessage(m_hComboU006En, CB_GETCURSEL, 0, 0);
            if (channel != CB_ERR && enable != CB_ERR) {
                SendGPIOCommand("U006", channel, enable);
            }
            break;
        }

        case ID_BUTTON_TEST_ALL:
            TestAllCommands();
            break;

        case ID_BUTTON_CLEAR:
            SetWindowText(m_hEditReceive, L"");
            AppendToReceiveBox(L"Display cleared");
            break;

        case ID_BUTTON_SEND:
            SendRawData();
            break;
        }
    }

    void TestAllCommands() {
        if (!m_bConnected) {
            AppendToReceiveBox(L"❌ Not connected - please connect first");
            return;
        }

        AppendToReceiveBox(L"=== Testing All Commands ===");

        // Test S-series
        SendCommand("S001", 5);
        Sleep(100);
        SendCommand("S002", 115200);
        Sleep(100);

        // Test U-series
        SendCommand("U001", 250);
        Sleep(100);
        SendCommand("U002", 1500);
        Sleep(100);
        SendCommand("U003", 3);
        Sleep(100);
        SendCommand("U004", 600);
        Sleep(100);
        SendGPIOCommand("U005", 0, 1);
        Sleep(100);
        SendGPIOCommand("U006", 1, 1);

        AppendToReceiveBox(L"=== All Commands Sent ===");
    }

    void SendRawData() {
        // Implementation for sending raw hex data
        wchar_t buffer[1024];
        GetWindowText(m_hEditSend, buffer, 1024);

        std::wstring hexData(buffer);
        if (hexData.empty()) {
            AppendToReceiveBox(L"❌ No data to send");
            return;
        }

        AppendToReceiveBox(L"Sending raw data: " + hexData);
        // Raw data sending implementation would go here
    }
};

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    RS485TestApp app;

    if (!app.Initialize(hInstance)) {
        MessageBox(nullptr, L"Failed to initialize application", L"Error", MB_OK | MB_ICONERROR);
        return -1;
    }

    MSG msg;
    while (GetMessage(&msg, nullptr, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    return (int)msg.wParam;
}
