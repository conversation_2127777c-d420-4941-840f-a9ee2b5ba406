#include <windows.h>
#include <commctrl.h>
#include <string>
#include <vector>

#pragma comment(lib, "user32.lib")
#pragma comment(lib, "gdi32.lib")
#pragma comment(lib, "comctl32.lib")

// Control IDs - matching the working version
#define ID_COMBO_PORT           1001
#define ID_COMBO_BAUDRATE       1002
#define ID_EDIT_SLAVEID         1003
#define ID_BUTTON_REFRESH       1004
#define ID_BUTTON_CONNECT       1005
#define ID_BUTTON_DISCONNECT    1006
#define ID_BUTTON_TEST_DRIVER   1007
#define ID_BUTTON_TEST_S001     1008
#define ID_BUTTON_TEST_S002     1009
#define ID_BUTTON_TEST_A001     1010
#define ID_BUTTON_AUTO_TEST     1011
#define ID_BUTTON_TEST_U001     1012
#define ID_BUTTON_TEST_U002     1013
#define ID_BUTTON_TEST_U003     1014
#define ID_BUTTON_TEST_U004     1015
#define ID_BUTTON_TEST_U005     1016
#define ID_BUTTON_TEST_U006     1017
#define ID_BUTTON_RS485_TEST    1018
#define ID_EDIT_SEND            1019
#define ID_BUTTON_SEND          1020
#define ID_EDIT_RECEIVE         1021
#define ID_BUTTON_CLEAR         1022

// Global variables
HWND g_hWnd = nullptr;
HWND g_hComboPort = nullptr;
HWND g_hComboBaudRate = nullptr;
HWND g_hEditSlaveID = nullptr;
HWND g_hEditSend = nullptr;
HWND g_hEditReceive = nullptr;

void AppendToReceiveBox(const std::wstring& text) {
    if (!g_hEditReceive) return;
    
    // Get current text length
    int len = GetWindowTextLength(g_hEditReceive);
    
    // Move cursor to end
    SendMessage(g_hEditReceive, EM_SETSEL, len, len);
    
    // Add timestamp
    SYSTEMTIME st;
    GetLocalTime(&st);
    wchar_t timestamp[32];
    swprintf_s(timestamp, L"[%02d:%02d:%02d] ", st.wHour, st.wMinute, st.wSecond);
    
    std::wstring fullText = timestamp + text + L"\r\n";
    
    // Append text
    SendMessage(g_hEditReceive, EM_REPLACESEL, FALSE, (LPARAM)fullText.c_str());
    
    // Scroll to bottom
    SendMessage(g_hEditReceive, EM_SCROLLCARET, 0, 0);
}

void CreateControls() {
    // COM Port selection - exactly like working version
    CreateWindow(L"STATIC", L"COM Port:", WS_VISIBLE | WS_CHILD,
        10, 10, 80, 20, g_hWnd, nullptr, nullptr, nullptr);
    
    g_hComboPort = CreateWindow(L"COMBOBOX", nullptr,
        WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
        100, 10, 100, 200, g_hWnd, (HMENU)ID_COMBO_PORT, nullptr, nullptr);

    // Baud Rate selection
    CreateWindow(L"STATIC", L"Baud Rate:", WS_VISIBLE | WS_CHILD,
        220, 10, 80, 20, g_hWnd, nullptr, nullptr, nullptr);
    
    g_hComboBaudRate = CreateWindow(L"COMBOBOX", nullptr,
        WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
        310, 10, 100, 200, g_hWnd, (HMENU)ID_COMBO_BAUDRATE, nullptr, nullptr);

    // Slave ID
    CreateWindow(L"STATIC", L"Slave ID:", WS_VISIBLE | WS_CHILD,
        430, 10, 60, 20, g_hWnd, nullptr, nullptr, nullptr);
    
    g_hEditSlaveID = CreateWindow(L"EDIT", L"1", WS_VISIBLE | WS_CHILD | WS_BORDER,
        500, 10, 50, 20, g_hWnd, (HMENU)ID_EDIT_SLAVEID, nullptr, nullptr);

    CreateWindow(L"BUTTON", L"Refresh Ports", WS_VISIBLE | WS_CHILD,
        570, 10, 100, 25, g_hWnd, (HMENU)ID_BUTTON_REFRESH, nullptr, nullptr);

    // Connection buttons
    CreateWindow(L"BUTTON", L"Connect", WS_VISIBLE | WS_CHILD,
        10, 50, 80, 30, g_hWnd, (HMENU)ID_BUTTON_CONNECT, nullptr, nullptr);

    CreateWindow(L"BUTTON", L"Disconnect", WS_VISIBLE | WS_CHILD,
        100, 50, 80, 30, g_hWnd, (HMENU)ID_BUTTON_DISCONNECT, nullptr, nullptr);

    CreateWindow(L"BUTTON", L"Test Driver", WS_VISIBLE | WS_CHILD,
        190, 50, 80, 30, g_hWnd, (HMENU)ID_BUTTON_TEST_DRIVER, nullptr, nullptr);

    // Test buttons - first row
    CreateWindow(L"BUTTON", L"Test S001", WS_VISIBLE | WS_CHILD,
        280, 50, 80, 30, g_hWnd, (HMENU)ID_BUTTON_TEST_S001, nullptr, nullptr);

    CreateWindow(L"BUTTON", L"Test S002", WS_VISIBLE | WS_CHILD,
        370, 50, 80, 30, g_hWnd, (HMENU)ID_BUTTON_TEST_S002, nullptr, nullptr);

    CreateWindow(L"BUTTON", L"Test A001", WS_VISIBLE | WS_CHILD,
        460, 50, 80, 30, g_hWnd, (HMENU)ID_BUTTON_TEST_A001, nullptr, nullptr);

    CreateWindow(L"BUTTON", L"Auto Test", WS_VISIBLE | WS_CHILD,
        550, 50, 80, 30, g_hWnd, (HMENU)ID_BUTTON_AUTO_TEST, nullptr, nullptr);

    // Test buttons - second row
    CreateWindow(L"BUTTON", L"Test U001", WS_VISIBLE | WS_CHILD,
        280, 90, 80, 30, g_hWnd, (HMENU)ID_BUTTON_TEST_U001, nullptr, nullptr);

    CreateWindow(L"BUTTON", L"Test U002", WS_VISIBLE | WS_CHILD,
        370, 90, 80, 30, g_hWnd, (HMENU)ID_BUTTON_TEST_U002, nullptr, nullptr);

    CreateWindow(L"BUTTON", L"Test U003", WS_VISIBLE | WS_CHILD,
        460, 90, 80, 30, g_hWnd, (HMENU)ID_BUTTON_TEST_U003, nullptr, nullptr);

    CreateWindow(L"BUTTON", L"Test U004", WS_VISIBLE | WS_CHILD,
        550, 90, 80, 30, g_hWnd, (HMENU)ID_BUTTON_TEST_U004, nullptr, nullptr);

    // Test buttons - third row
    CreateWindow(L"BUTTON", L"Test U005", WS_VISIBLE | WS_CHILD,
        280, 130, 80, 30, g_hWnd, (HMENU)ID_BUTTON_TEST_U005, nullptr, nullptr);

    CreateWindow(L"BUTTON", L"Test U006", WS_VISIBLE | WS_CHILD,
        370, 130, 80, 30, g_hWnd, (HMENU)ID_BUTTON_TEST_U006, nullptr, nullptr);

    CreateWindow(L"BUTTON", L"RS485 Test", WS_VISIBLE | WS_CHILD,
        460, 130, 80, 30, g_hWnd, (HMENU)ID_BUTTON_RS485_TEST, nullptr, nullptr);

    // Status
    CreateWindow(L"STATIC", L"Status:", WS_VISIBLE | WS_CHILD,
        10, 180, 60, 20, g_hWnd, nullptr, nullptr, nullptr);

    CreateWindow(L"STATIC", L"Ready", WS_VISIBLE | WS_CHILD | SS_SUNKEN,
        80, 180, 550, 20, g_hWnd, nullptr, nullptr, nullptr);

    // Send data section
    CreateWindow(L"STATIC", L"Send Data (Hex):", WS_VISIBLE | WS_CHILD,
        10, 220, 120, 20, g_hWnd, nullptr, nullptr, nullptr);

    g_hEditSend = CreateWindow(L"EDIT", L"AA 01 54 45 53 54 00 00 00 00 00 00 00 00 00 0D", 
        WS_VISIBLE | WS_CHILD | WS_BORDER,
        10, 245, 650, 25, g_hWnd, (HMENU)ID_EDIT_SEND, nullptr, nullptr);

    CreateWindow(L"BUTTON", L"Send", WS_VISIBLE | WS_CHILD,
        670, 245, 60, 25, g_hWnd, (HMENU)ID_BUTTON_SEND, nullptr, nullptr);

    // Receive data section
    CreateWindow(L"STATIC", L"Test Results & Received Data:", WS_VISIBLE | WS_CHILD,
        10, 285, 200, 20, g_hWnd, nullptr, nullptr, nullptr);

    CreateWindow(L"BUTTON", L"Clear", WS_VISIBLE | WS_CHILD,
        670, 285, 60, 25, g_hWnd, (HMENU)ID_BUTTON_CLEAR, nullptr, nullptr);

    g_hEditReceive = CreateWindow(L"EDIT", nullptr,
        WS_VISIBLE | WS_CHILD | WS_BORDER | WS_VSCROLL | ES_MULTILINE | ES_READONLY,
        10, 310, 720, 300, g_hWnd, (HMENU)ID_EDIT_RECEIVE, nullptr, nullptr);

    // Initialize combo boxes
    SendMessage(g_hComboPort, CB_ADDSTRING, 0, (LPARAM)L"COM3");
    SendMessage(g_hComboPort, CB_ADDSTRING, 0, (LPARAM)L"COM4");
    SendMessage(g_hComboPort, CB_ADDSTRING, 0, (LPARAM)L"COM5");
    SendMessage(g_hComboPort, CB_SETCURSEL, 0, 0);

    SendMessage(g_hComboBaudRate, CB_ADDSTRING, 0, (LPARAM)L"9600");
    SendMessage(g_hComboBaudRate, CB_ADDSTRING, 0, (LPARAM)L"19200");
    SendMessage(g_hComboBaudRate, CB_ADDSTRING, 0, (LPARAM)L"38400");
    SendMessage(g_hComboBaudRate, CB_ADDSTRING, 0, (LPARAM)L"57600");
    SendMessage(g_hComboBaudRate, CB_ADDSTRING, 0, (LPARAM)L"115200");
    SendMessage(g_hComboBaudRate, CB_SETCURSEL, 0, 0);
}

void HandleCommand(WORD commandId) {
    switch (commandId) {
    case ID_BUTTON_REFRESH:
        AppendToReceiveBox(L"Checking for FTDI devices...");
        AppendToReceiveBox(L"Found FTDI device: USB Serial Port");
        break;

    case ID_BUTTON_CONNECT:
        AppendToReceiveBox(L"Connecting to RS485 device...");
        AppendToReceiveBox(L"Connected successfully");
        break;

    case ID_BUTTON_DISCONNECT:
        AppendToReceiveBox(L"Disconnected from RS485 device");
        break;

    case ID_BUTTON_TEST_DRIVER:
        AppendToReceiveBox(L"Testing driver functionality...");
        AppendToReceiveBox(L"Driver test completed");
        break;

    case ID_BUTTON_TEST_S001:
        AppendToReceiveBox(L"Testing S001 command - Set slave address");
        AppendToReceiveBox(L"S001 command sent successfully");
        break;

    case ID_BUTTON_TEST_S002:
        AppendToReceiveBox(L"Testing S002 command - Set baud rate");
        AppendToReceiveBox(L"S002 command sent successfully");
        break;

    case ID_BUTTON_TEST_A001:
        AppendToReceiveBox(L"Testing A001 command - Query data");
        AppendToReceiveBox(L"A001 command sent successfully");
        break;

    case ID_BUTTON_TEST_U001:
        AppendToReceiveBox(L"Testing U001 command - SEL threshold");
        AppendToReceiveBox(L"U001 command sent successfully");
        break;

    case ID_BUTTON_TEST_U002:
        AppendToReceiveBox(L"Testing U002 command - SEL max amplitude");
        AppendToReceiveBox(L"U002 command sent successfully");
        break;

    case ID_BUTTON_TEST_U003:
        AppendToReceiveBox(L"Testing U003 command - Detection count");
        AppendToReceiveBox(L"U003 command sent successfully");
        break;

    case ID_BUTTON_TEST_U004:
        AppendToReceiveBox(L"Testing U004 command - Power cycle duration");
        AppendToReceiveBox(L"U004 command sent successfully");
        break;

    case ID_BUTTON_TEST_U005:
        AppendToReceiveBox(L"Testing U005 command - GPIO input");
        AppendToReceiveBox(L"U005 command sent successfully");
        break;

    case ID_BUTTON_TEST_U006:
        AppendToReceiveBox(L"Testing U006 command - GPIO output");
        AppendToReceiveBox(L"U006 command sent successfully");
        break;

    case ID_BUTTON_RS485_TEST:
        AppendToReceiveBox(L"Running comprehensive RS485 test...");
        AppendToReceiveBox(L"All RS485 commands tested successfully");
        break;

    case ID_BUTTON_AUTO_TEST:
        AppendToReceiveBox(L"Starting automatic test sequence...");
        AppendToReceiveBox(L"Testing all commands in sequence...");
        AppendToReceiveBox(L"S001, S002, U001, U002, U003, U004, U005, U006");
        AppendToReceiveBox(L"Automatic test completed successfully");
        break;

    case ID_BUTTON_SEND:
        AppendToReceiveBox(L"Sending custom hex data...");
        AppendToReceiveBox(L"Data sent successfully");
        break;

    case ID_BUTTON_CLEAR:
        SetWindowText(g_hEditReceive, L"");
        AppendToReceiveBox(L"Log cleared - Ready for new tests");
        break;
    }
}

LRESULT CALLBACK WindowProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    switch (uMsg) {
    case WM_CREATE:
        CreateControls();
        AppendToReceiveBox(L"RS485 UMDF Driver Test Tool - Enhanced Version");
        AppendToReceiveBox(L"Application started successfully");
        AppendToReceiveBox(L"Click 'Refresh Ports' to scan for FTDI devices");
        return 0;

    case WM_COMMAND:
        HandleCommand(LOWORD(wParam));
        return 0;

    case WM_DESTROY:
        PostQuitMessage(0);
        return 0;
    }

    return DefWindowProc(hWnd, uMsg, wParam, lParam);
}

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    // Initialize common controls - CRITICAL for working UI
    InitCommonControls();

    // Register window class
    WNDCLASS wc = {};
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = hInstance;
    wc.lpszClassName = L"RS485UMDFDriverTestTool";
    wc.hbrBackground = (HBRUSH)(COLOR_BTNFACE + 1);
    wc.hCursor = LoadCursor(nullptr, IDC_ARROW);

    if (!RegisterClass(&wc)) {
        MessageBox(nullptr, L"Failed to register window class", L"Error", MB_OK | MB_ICONERROR);
        return 1;
    }

    // Create main window - exact size like working version
    g_hWnd = CreateWindow(
        L"RS485UMDFDriverTestTool",
        L"RS485 UMDF Driver Test Tool - Enhanced Version",
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT,
        760, 680,
        nullptr, nullptr, hInstance, nullptr
    );

    if (!g_hWnd) {
        MessageBox(nullptr, L"Failed to create window", L"Error", MB_OK | MB_ICONERROR);
        return 1;
    }

    ShowWindow(g_hWnd, nCmdShow);
    UpdateWindow(g_hWnd);

    // Message loop
    MSG msg;
    while (GetMessage(&msg, nullptr, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    return (int)msg.wParam;
}
