#include <windows.h>

LRESULT CALLBACK WindowProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    switch (uMsg) {
    case WM_CREATE:
        // Create a simple button to test
        CreateWindow(L"BUTTON", L"Test Button", 
            WS_VISIBLE | WS_CHILD,
            50, 50, 100, 30, 
            hWnd, (HMENU)1001, GetModule<PERSON>andle(nullptr), nullptr);
        
        // Create a static text
        CreateWindow(L"STATIC", L"Hello World - RS485 Test", 
            WS_VISIBLE | WS_CHILD,
            50, 20, 200, 20, 
            hWnd, nullptr, GetModuleHandle(nullptr), nullptr);
        
        return 0;

    case WM_COMMAND:
        if (LOWORD(wParam) == 1001) {
            MessageBox(hWnd, L"Button clicked!", L"Test", MB_OK);
        }
        return 0;

    case WM_DESTROY:
        PostQuitMessage(0);
        return 0;

    case WM_PAINT: {
        PAINTSTRUCT ps;
        HDC hdc = BeginPaint(hWnd, &ps);
        
        // Draw something to verify painting works
        TextOut(hdc, 10, 100, L"Paint message received", 22);
        
        EndPaint(hWnd, &ps);
        return 0;
    }
    }

    return DefWindowProc(hWnd, uMsg, wParam, lParam);
}

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    // Show a message box first to confirm the app starts
    MessageBox(nullptr, L"Starting minimal RS485 test application", L"Debug", MB_OK);
    
    // Register window class
    WNDCLASS wc = {};
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = hInstance;
    wc.lpszClassName = L"MinimalRS485Test";
    wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    wc.hCursor = LoadCursor(nullptr, IDC_ARROW);

    if (!RegisterClass(&wc)) {
        MessageBox(nullptr, L"Failed to register window class", L"Error", MB_OK);
        return -1;
    }

    // Create window
    HWND hWnd = CreateWindowEx(
        0,
        L"MinimalRS485Test",
        L"Minimal RS485 Test",
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT,
        400, 300,
        nullptr, nullptr, hInstance, nullptr
    );

    if (!hWnd) {
        MessageBox(nullptr, L"Failed to create window", L"Error", MB_OK);
        return -1;
    }

    ShowWindow(hWnd, nCmdShow);
    UpdateWindow(hWnd);

    // Show another message to confirm we got this far
    MessageBox(nullptr, L"Window created successfully, starting message loop", L"Debug", MB_OK);

    // Message loop
    MSG msg;
    while (GetMessage(&msg, nullptr, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    return (int)msg.wParam;
}
