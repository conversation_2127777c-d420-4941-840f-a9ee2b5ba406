#include <windows.h>
#include <commctrl.h>
#include <string>

#pragma comment(lib, "user32.lib")
#pragma comment(lib, "gdi32.lib")
#pragma comment(lib, "comctl32.lib")

// Window dimensions
#define WINDOW_WIDTH 800
#define WINDOW_HEIGHT 600

// Control IDs
#define ID_BUTTON_TEST      1001
#define ID_EDIT_OUTPUT      1002
#define ID_STATIC_LABEL     1003

HINSTANCE g_hInstance = nullptr;
HWND g_hMainWindow = nullptr;
HWND g_hEditOutput = nullptr;

void LogMessage(const std::wstring& message) {
    if (!g_hEditOutput) return;
    
    // Get current text length
    int textLength = GetWindowTextLength(g_hEditOutput);
    
    // Move cursor to end
    SendMessage(g_hEditOutput, EM_SETSEL, textLength, textLength);
    
    // Add timestamp
    SYSTEMTIME st;
    GetLocalTime(&st);
    wchar_t timestamp[64];
    swprintf_s(timestamp, L"[%02d:%02d:%02d] ", st.wHour, st.wMinute, st.wSecond);
    
    std::wstring fullText = timestamp + message + L"\r\n";
    
    // Append text
    SendMessage(g_hEditOutput, EM_REPLACESEL, FALSE, (LPARAM)fullText.c_str());
    
    // Scroll to bottom
    SendMessage(g_hEditOutput, EM_SCROLLCARET, 0, 0);
}

void CreateControls() {
    if (!g_hMainWindow) return;
    
    // Create a test label
    CreateWindow(L"STATIC", L"RS485 Test Application - Simple Version", 
        WS_VISIBLE | WS_CHILD | SS_LEFT,
        20, 20, 400, 20, g_hMainWindow, (HMENU)ID_STATIC_LABEL, g_hInstance, nullptr);
    
    // Create a test button
    CreateWindow(L"BUTTON", L"Test Button", WS_VISIBLE | WS_CHILD,
        20, 50, 100, 30, g_hMainWindow, (HMENU)ID_BUTTON_TEST, g_hInstance, nullptr);
    
    // Create output text area
    g_hEditOutput = CreateWindow(L"EDIT", nullptr,
        WS_VISIBLE | WS_CHILD | WS_BORDER | WS_VSCROLL | ES_MULTILINE | ES_READONLY,
        20, 90, 750, 450, g_hMainWindow, (HMENU)ID_EDIT_OUTPUT, g_hInstance, nullptr);
    
    // Force window update
    UpdateWindow(g_hMainWindow);
    InvalidateRect(g_hMainWindow, nullptr, TRUE);
}

void HandleCommand(WORD commandId) {
    switch (commandId) {
    case ID_BUTTON_TEST:
        LogMessage(L"Test button clicked!");
        MessageBox(g_hMainWindow, L"Test button works!", L"Success", MB_OK | MB_ICONINFORMATION);
        break;
    }
}

LRESULT CALLBACK WindowProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    switch (uMsg) {
    case WM_CREATE:
        CreateControls();
        // Post message to log after controls are created
        PostMessage(hWnd, WM_USER + 1, 0, 0);
        return 0;
        
    case WM_USER + 1:
        LogMessage(L"Simple RS485 Test Application started");
        LogMessage(L"Click the test button to verify functionality");
        return 0;

    case WM_COMMAND:
        HandleCommand(LOWORD(wParam));
        return 0;

    case WM_DESTROY:
        PostQuitMessage(0);
        return 0;

    case WM_PAINT: {
        PAINTSTRUCT ps;
        HDC hdc = BeginPaint(hWnd, &ps);
        
        // Draw a simple background
        RECT rect;
        GetClientRect(hWnd, &rect);
        FillRect(hdc, &rect, (HBRUSH)(COLOR_BTNFACE + 1));
        
        EndPaint(hWnd, &ps);
        return 0;
    }
    }

    return DefWindowProc(hWnd, uMsg, wParam, lParam);
}

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    g_hInstance = hInstance;
    
    // Initialize common controls
    INITCOMMONCONTROLSEX icex;
    icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
    icex.dwICC = ICC_WIN95_CLASSES;
    if (!InitCommonControlsEx(&icex)) {
        MessageBox(nullptr, L"Failed to initialize common controls", L"Error", MB_OK | MB_ICONERROR);
        return -1;
    }

    // Register window class
    WNDCLASSEX wc = {};
    wc.cbSize = sizeof(WNDCLASSEX);
    wc.style = CS_HREDRAW | CS_VREDRAW;
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = hInstance;
    wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
    wc.hbrBackground = (HBRUSH)(COLOR_BTNFACE + 1);
    wc.lpszClassName = L"RS485SimpleTest";
    wc.hIcon = LoadIcon(nullptr, IDI_APPLICATION);
    wc.hIconSm = LoadIcon(nullptr, IDI_APPLICATION);

    if (!RegisterClassEx(&wc)) {
        MessageBox(nullptr, L"Failed to register window class", L"Error", MB_OK | MB_ICONERROR);
        return -1;
    }

    // Create main window
    g_hMainWindow = CreateWindowEx(
        0,
        L"RS485SimpleTest",
        L"RS485 Test Application - Simple Version",
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT,
        WINDOW_WIDTH, WINDOW_HEIGHT,
        nullptr, nullptr, hInstance, nullptr
    );

    if (!g_hMainWindow) {
        MessageBox(nullptr, L"Failed to create main window", L"Error", MB_OK | MB_ICONERROR);
        return -1;
    }

    ShowWindow(g_hMainWindow, nCmdShow);
    UpdateWindow(g_hMainWindow);

    // Message loop
    MSG msg;
    while (GetMessage(&msg, nullptr, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    return (int)msg.wParam;
}
