#include <windows.h>
#include <commctrl.h>
#include <string>
#include <vector>

#pragma comment(lib, "user32.lib")
#pragma comment(lib, "gdi32.lib")
#pragma comment(lib, "comctl32.lib")

// Control IDs
#define ID_BUTTON_CONNECT       1001
#define ID_BUTTON_DISCONNECT    1002
#define ID_COMBO_PORT           1003
#define ID_BUTTON_REFRESH       1004
#define ID_EDIT_LOG             1005
#define ID_BUTTON_S001          1006
#define ID_EDIT_S001            1007
#define ID_BUTTON_TEST_ALL      1008

HINSTANCE g_hInstance = nullptr;
HWND g_hMainWindow = nullptr;
HWND g_hLogEdit = nullptr;

void LogMessage(const std::wstring& message) {
    if (!g_hLogEdit) return;
    
    // Get current text length
    int textLength = GetWindowTextLength(g_hLogEdit);
    
    // Move cursor to end
    SendMessage(g_hLogEdit, EM_SETSEL, textLength, textLength);
    
    // Add message with newline
    std::wstring fullText = message + L"\r\n";
    SendMessage(g_hLogEdit, EM_REPLACESEL, FALSE, (LPARAM)fullText.c_str());
    
    // Scroll to bottom
    SendMessage(g_hLogEdit, EM_SCROLLCARET, 0, 0);
}

void CreateAllControls(HWND hWnd) {
    // Title
    CreateWindow(L"STATIC", L"RS485 Driver Test Application", 
        WS_VISIBLE | WS_CHILD | SS_CENTER,
        20, 20, 760, 25, hWnd, nullptr, g_hInstance, nullptr);
    
    // Connection section
    CreateWindow(L"STATIC", L"Connection:", 
        WS_VISIBLE | WS_CHILD,
        20, 60, 100, 20, hWnd, nullptr, g_hInstance, nullptr);
    
    CreateWindow(L"COMBOBOX", nullptr,
        WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
        130, 58, 120, 200, hWnd, (HMENU)ID_COMBO_PORT, g_hInstance, nullptr);
    
    CreateWindow(L"BUTTON", L"Refresh", WS_VISIBLE | WS_CHILD,
        260, 58, 70, 25, hWnd, (HMENU)ID_BUTTON_REFRESH, g_hInstance, nullptr);
    
    CreateWindow(L"BUTTON", L"Connect", WS_VISIBLE | WS_CHILD,
        340, 58, 70, 25, hWnd, (HMENU)ID_BUTTON_CONNECT, g_hInstance, nullptr);
    
    CreateWindow(L"BUTTON", L"Disconnect", WS_VISIBLE | WS_CHILD,
        420, 58, 80, 25, hWnd, (HMENU)ID_BUTTON_DISCONNECT, g_hInstance, nullptr);
    
    // S001 Command
    CreateWindow(L"STATIC", L"S001 - Slave Address (1-31):", 
        WS_VISIBLE | WS_CHILD,
        20, 100, 200, 20, hWnd, nullptr, g_hInstance, nullptr);
    
    CreateWindow(L"EDIT", L"5", WS_VISIBLE | WS_CHILD | WS_BORDER,
        230, 98, 50, 25, hWnd, (HMENU)ID_EDIT_S001, g_hInstance, nullptr);
    
    CreateWindow(L"BUTTON", L"Send S001", WS_VISIBLE | WS_CHILD,
        290, 98, 80, 25, hWnd, (HMENU)ID_BUTTON_S001, g_hInstance, nullptr);
    
    // Test All button
    CreateWindow(L"BUTTON", L"Test All Commands", WS_VISIBLE | WS_CHILD,
        20, 140, 150, 35, hWnd, (HMENU)ID_BUTTON_TEST_ALL, g_hInstance, nullptr);
    
    // Log area
    CreateWindow(L"STATIC", L"Log Output:", 
        WS_VISIBLE | WS_CHILD,
        20, 190, 100, 20, hWnd, nullptr, g_hInstance, nullptr);
    
    g_hLogEdit = CreateWindow(L"EDIT", nullptr,
        WS_VISIBLE | WS_CHILD | WS_BORDER | WS_VSCROLL | ES_MULTILINE | ES_READONLY,
        20, 215, 760, 300, hWnd, (HMENU)ID_EDIT_LOG, g_hInstance, nullptr);
    
    // Force window update
    UpdateWindow(hWnd);
    InvalidateRect(hWnd, nullptr, TRUE);
    
    // Log initial message
    LogMessage(L"RS485 Test Application Started");
    LogMessage(L"All controls created successfully");
}

void HandleCommand(WORD commandId) {
    switch (commandId) {
    case ID_BUTTON_REFRESH:
        LogMessage(L"Refresh button clicked");
        break;
        
    case ID_BUTTON_CONNECT:
        LogMessage(L"Connect button clicked");
        break;
        
    case ID_BUTTON_DISCONNECT:
        LogMessage(L"Disconnect button clicked");
        break;
        
    case ID_BUTTON_S001:
        LogMessage(L"S001 command button clicked");
        break;
        
    case ID_BUTTON_TEST_ALL:
        LogMessage(L"Test All Commands button clicked");
        LogMessage(L"This would test all RS485 commands");
        break;
    }
}

LRESULT CALLBACK WindowProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    switch (uMsg) {
    case WM_CREATE:
        CreateAllControls(hWnd);
        return 0;

    case WM_COMMAND:
        HandleCommand(LOWORD(wParam));
        return 0;

    case WM_DESTROY:
        PostQuitMessage(0);
        return 0;

    case WM_PAINT: {
        PAINTSTRUCT ps;
        HDC hdc = BeginPaint(hWnd, &ps);
        
        // Fill background
        RECT rect;
        GetClientRect(hWnd, &rect);
        FillRect(hdc, &rect, (HBRUSH)(COLOR_BTNFACE + 1));
        
        EndPaint(hWnd, &ps);
        return 0;
    }
    
    case WM_CTLCOLORSTATIC:
    case WM_CTLCOLOREDIT:
        // Ensure proper background for controls
        return (LRESULT)GetStockObject(WHITE_BRUSH);
    }

    return DefWindowProc(hWnd, uMsg, wParam, lParam);
}

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    g_hInstance = hInstance;
    
    // Initialize common controls
    INITCOMMONCONTROLSEX icex;
    icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
    icex.dwICC = ICC_WIN95_CLASSES;
    InitCommonControlsEx(&icex);

    // Register window class
    WNDCLASSEX wc = {};
    wc.cbSize = sizeof(WNDCLASSEX);
    wc.style = CS_HREDRAW | CS_VREDRAW;
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = hInstance;
    wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
    wc.hbrBackground = (HBRUSH)(COLOR_BTNFACE + 1);
    wc.lpszClassName = L"RS485WorkingTest";
    wc.hIcon = LoadIcon(nullptr, IDI_APPLICATION);
    wc.hIconSm = LoadIcon(nullptr, IDI_APPLICATION);

    if (!RegisterClassEx(&wc)) {
        MessageBox(nullptr, L"Failed to register window class", L"Error", MB_OK | MB_ICONERROR);
        return -1;
    }

    // Create main window
    g_hMainWindow = CreateWindowEx(
        0,
        L"RS485WorkingTest",
        L"RS485 Driver Test Application - Working Version",
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT,
        820, 600,
        nullptr, nullptr, hInstance, nullptr
    );

    if (!g_hMainWindow) {
        MessageBox(nullptr, L"Failed to create main window", L"Error", MB_OK | MB_ICONERROR);
        return -1;
    }

    ShowWindow(g_hMainWindow, nCmdShow);
    UpdateWindow(g_hMainWindow);

    // Message loop
    MSG msg;
    while (GetMessage(&msg, nullptr, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    return (int)msg.wParam;
}
