//
// RS485 Driver Test Application
// Basic functionality test for RS485 driver interface
//

#include "../Interface/RS485DriverInterface.h"
#include <iostream>
#include <vector>
#include <chrono>
#include <thread>

using namespace std;

//
// Test Functions
//
bool TestDeviceEnumeration();
bool TestDriverConnection();
bool TestSystemConfiguration();
bool TestUserConfiguration();
bool TestDataRequest();
bool TestBufferManagement();

//
// Main Test Function
//
int main() {
    cout << "RS485 Driver Test Application" << endl;
    cout << "=============================" << endl;

    bool allTestsPassed = true;

    // Test 1: Device Enumeration
    cout << "\n1. Testing Device Enumeration..." << endl;
    if (TestDeviceEnumeration()) {
        cout << "   PASSED: Device enumeration successful" << endl;
    } else {
        cout << "   FAILED: Device enumeration failed" << endl;
        allTestsPassed = false;
    }

    // Test 2: Driver Connection
    cout << "\n2. Testing Driver Connection..." << endl;
    if (TestDriverConnection()) {
        cout << "   PASSED: Driver connection successful" << endl;
    } else {
        cout << "   FAILED: Driver connection failed" << endl;
        allTestsPassed = false;
    }

    // Test 3: System Configuration
    cout << "\n3. Testing System Configuration..." << endl;
    if (TestSystemConfiguration()) {
        cout << "   PASSED: System configuration successful" << endl;
    } else {
        cout << "   FAILED: System configuration failed" << endl;
        allTestsPassed = false;
    }

    // Test 4: User Configuration
    cout << "\n4. Testing User Configuration..." << endl;
    if (TestUserConfiguration()) {
        cout << "   PASSED: User configuration successful" << endl;
    } else {
        cout << "   FAILED: User configuration failed" << endl;
        allTestsPassed = false;
    }

    // Test 5: Data Request
    cout << "\n5. Testing Data Request..." << endl;
    if (TestDataRequest()) {
        cout << "   PASSED: Data request successful" << endl;
    } else {
        cout << "   FAILED: Data request failed" << endl;
        allTestsPassed = false;
    }

    // Test 6: Buffer Management
    cout << "\n6. Testing Buffer Management..." << endl;
    if (TestBufferManagement()) {
        cout << "   PASSED: Buffer management successful" << endl;
    } else {
        cout << "   FAILED: Buffer management failed" << endl;
        allTestsPassed = false;
    }

    // Summary
    cout << "\n=============================" << endl;
    if (allTestsPassed) {
        cout << "ALL TESTS PASSED!" << endl;
        return 0;
    } else {
        cout << "SOME TESTS FAILED!" << endl;
        return 1;
    }
}

//
// Test Device Enumeration
//
bool TestDeviceEnumeration() {
    try {
        vector<DeviceInfo> devices;
        RS485_ENUMERATION_RESULT result = RS485DriverInterface::enumerateDevices(devices);

        if (result == EnumerationResultSuccess) {
            cout << "   Found " << devices.size() << " device(s):" << endl;
            for (const auto& device : devices) {
                cout << "     - " << device.description << " (" << device.port << ")" << endl;
                cout << "       VID:PID = " << hex << device.vendorId << ":" << device.productId << dec << endl;
                cout << "       Driver loaded: " << (device.isDriverLoaded ? "Yes" : "No") << endl;
            }
            return true;
        } else if (result == EnumerationResultNoDevicesFound) {
            cout << "   No devices found (this may be expected if no hardware is connected)" << endl;
            return true; // Not a failure if no devices are connected
        } else {
            cout << "   Enumeration failed with error: " << static_cast<int>(result) << endl;
            return false;
        }
    } catch (const exception& e) {
        cout << "   Exception during enumeration: " << e.what() << endl;
        return false;
    }
}

//
// Test Driver Connection
//
bool TestDriverConnection() {
    try {
        RS485DriverInterface driver;

        // First enumerate devices to get a device path
        vector<DeviceInfo> devices;
        RS485_ENUMERATION_RESULT enumResult = RS485DriverInterface::enumerateDevices(devices);

        if (enumResult != EnumerationResultSuccess || devices.empty()) {
            cout << "   No devices available for connection test" << endl;
            return true; // Not a failure if no devices are available
        }

        // Try to open the first device
        string devicePath = devices[0].port;
        RS485_CONNECTION_RESULT result = driver.openPort(devicePath);

        if (result == ConnectionResultSuccess) {
            cout << "   Successfully opened device: " << devicePath << endl;

            // Test port status
            if (driver.isPortOpen()) {
                cout << "   Port status: Open" << endl;
            } else {
                cout << "   ERROR: Port should be open but reports closed" << endl;
                return false;
            }

            // Get port information
            PortInfo portInfo;
            RS485_PORT_RESULT portResult = driver.getPortInfo(portInfo);
            if (portResult == PortResultSuccess) {
                cout << "   Port info: " << portInfo.friendlyName << endl;
                cout << "   Baud rate: " << portInfo.baudRate << endl;
            }

            // Close the port
            RS485_CONNECTION_RESULT closeResult = driver.closePort();
            if (closeResult == ConnectionResultSuccess) {
                cout << "   Successfully closed device" << endl;
                return true;
            } else {
                cout << "   ERROR: Failed to close device" << endl;
                return false;
            }
        } else {
            cout << "   Failed to open device: " << static_cast<int>(result) << endl;
            return false;
        }
    } catch (const exception& e) {
        cout << "   Exception during connection test: " << e.what() << endl;
        return false;
    }
}

//
// Test System Configuration
//
bool TestSystemConfiguration() {
    try {
        RS485DriverInterface driver;

        // For this test, we'll simulate the configuration without actual hardware
        cout << "   Testing system configuration API calls..." << endl;

        // Test simple API functions
        RS485_CONFIGURATION_RESULT result1 = RS485SimpleAPI::setSlaveAddress(driver, 5);
        cout << "   Set slave address result: " << static_cast<int>(result1) << endl;



        // These will likely fail without hardware, but we're testing the API structure
        return true;
    } catch (const exception& e) {
        cout << "   Exception during system configuration test: " << e.what() << endl;
        return false;
    }
}

//
// Test User Configuration
//
bool TestUserConfiguration() {
    try {
        RS485DriverInterface driver;

        cout << "   Testing user configuration API calls..." << endl;

        // Test configuration functions
        RS485_CONFIGURATION_RESULT result1 = RS485SimpleAPI::configureSELThreshold(driver, 250);
        cout << "   Configure SEL threshold result: " << static_cast<int>(result1) << endl;

        RS485_CONFIGURATION_RESULT result2 = RS485SimpleAPI::configureMaxAmplitude(driver, 1500);
        cout << "   Configure max amplitude result: " << static_cast<int>(result2) << endl;

        RS485_CONFIGURATION_RESULT result3 = RS485SimpleAPI::configurePowerCycleDuration(driver, 600);
        cout << "   Configure power cycle duration result: " << static_cast<int>(result3) << endl;

        RS485_CONFIGURATION_RESULT result4 = RS485SimpleAPI::configureGPIOInput(driver, 0, true);
        cout << "   Configure GPIO input result: " << static_cast<int>(result4) << endl;

        // These will likely fail without hardware, but we're testing the API structure
        return true;
    } catch (const exception& e) {
        cout << "   Exception during user configuration test: " << e.what() << endl;
        return false;
    }
}

//
// Test Data Request
//
bool TestDataRequest() {
    try {
        RS485DriverInterface driver;

        cout << "   Testing data request API calls..." << endl;

        // Test data request
        RS485_REQUEST_RESULT result = driver.requestData("A001");
        cout << "   Request data result: " << static_cast<int>(result) << endl;

        // Test response check
        bool isDataReady = false;
        RS485_RESPONSE_RESULT checkResult = driver.checkSlaveDataReady(5, isDataReady);
        cout << "   Check data ready result: " << static_cast<int>(checkResult) << endl;
        cout << "   Data ready: " << (isDataReady ? "Yes" : "No") << endl;

        // These will likely fail without hardware, but we're testing the API structure
        return true;
    } catch (const exception& e) {
        cout << "   Exception during data request test: " << e.what() << endl;
        return false;
    }
}

//
// Test Buffer Management
//
bool TestBufferManagement() {
    try {
        RS485DriverInterface driver;

        cout << "   Testing buffer management API calls..." << endl;

        // Test buffer status
        BufferStatus status;
        RS485_BUFFER_RESULT result = driver.getBufferStatus(status);
        cout << "   Get buffer status result: " << static_cast<int>(result) << endl;

        if (result == BufferResultSuccess) {
            cout << "   Uplink buffer: " << status.uplinkUsed << "/" << status.uplinkTotal << endl;
            cout << "   Downlink buffer: " << status.downlinkUsed << "/" << status.downlinkTotal << endl;
            cout << "   Payload size: " << status.payloadSize << " bytes" << endl;
            cout << "   Total buffer size: " << status.totalBufferBytes << " bytes" << endl;
        }

        // Test buffer availability checks
        bool isUplinkFull = false;
        RS485_BUFFER_RESULT uplinkResult = driver.checkUplinkBufferAvailability(isUplinkFull);
        cout << "   Uplink buffer check result: " << static_cast<int>(uplinkResult) << endl;
        cout << "   Uplink buffer full: " << (isUplinkFull ? "Yes" : "No") << endl;

        // These will likely fail without hardware, but we're testing the API structure
        return true;
    } catch (const exception& e) {
        cout << "   Exception during buffer management test: " << e.what() << endl;
        return false;
    }
}
